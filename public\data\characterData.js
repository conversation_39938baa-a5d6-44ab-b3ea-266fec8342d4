// data/characterData.js

export const characters = [
    { name: "<PERSON>", image: "/imagens/mario.jpeg" },
    { name: "<PERSON>", image: "/imagens/link.webp" },
    { name: "<PERSON><PERSON><PERSON>", image: "/imagens/pikachu.avif" },
    { name: "<PERSON>", image: "/imagens/kirby.jpg" },
    { name: "Master Chief", image: "/imagens/halo.webp" },
    { name: "<PERSON>", image: "/imagens/lara.webp" },
    { name: "<PERSON>", image: "/imagens/steve.jpeg" },
    { name: "Donkey", image: "/imagens/donkey.jpg" },
    { name: "<PERSON><PERSON>", image: "/imagens/megaman.jpg" },
    { name: "<PERSON><PERSON>", image: "/imagens/ryu.webp" },
    { name: "<PERSON><PERSON>", image: "/imagens/banjo.jpg" },
    { name: "<PERSON><PERSON>", image: "/imagens/bowser.png" },
    { name: "<PERSON>", image: "/imagens/crash.webp" },
    { name: "<PERSON><PERSON>", image: "/imagens/eggman.webp" },
    { name: "<PERSON>", image: "/imagens/ellie.webp" },
    { name: "<PERSON>", image: "/imagens/ezio.jpg" },
    { name: "<PERSON>", image: "/imagens/gordon.png" },
    { name: "Kratos", image: "/imagens/kratos.webp" },
    { name: "Mewtwo", image: "/imagens/mewtwo.png" },
    { name: "Rayman", image: "/imagens/rayman.jpg" },
    { name: "Shadow", image: "/imagens/shadow.webp" },
    { name: "Snake", image: "/imagens/snake.webp" },
    { name: "Sonic", image: "/imagens/sonic.png" },
    { name: "Sora", image: "/imagens/sora.jpg" },
    { name: "Spyro", image: "/imagens/spyro.png" },
    { name: "Tail", image: "/imagens/tails.webp" },
    { name: "Uncharted", image: "/imagens/uncharted.jpg" },
    { name: "Wario", image: "/imagens/wario.png" },
    { name: "Zelda", image: "/imagens/zelda.webp" },
    { name: "Rato", image: "/imagens/rato.webp" },
    { name: "Okami", image: "/imagens/okami.webp" },
    { name: "CJ", image: "/imagens/cj.webp" },
    { name: "Sans", image: "/imagens/sans.jpg" },
    { name: "Hollow", image: "/imagens/hollow.jpg" },
    { name: "Ralsei", image: "/imagens/ralsei.webp" },
    { name: "Yi", image: "/imagens/yi.webp" },
    { name: "Laika", image: "/imagens/laika.png" },
    { name: "Madeline", image: "/imagens/madeline.jpg" },
    { name: "Jimmy", image: "/imagens/jimmy.webp" },
    { name: "Zumbi", image: "/imagens/zumbi.jpg" },
    { name: "Hades", image: "/imagens/hades.jpg" },
    { name: "Lamb", image: "/imagens/lamb.webp" },
    { name: "Xicrinho", image: "/imagens/xicrinho.jpeg" },
    { name: "Ori", image: "/imagens/ori.jpg" },
    { name: "Limbo", image: "/imagens/limbo.png" },
    { name: "Pizza", image: "/imagens/pizza.jpg" },
    { name: "Asriel", image: "/imagens/asriel.webp" },
    { name: "Bomberman", image: "/imagens/bomberman.webp" },
    { name: "Pacman", image: "/imagens/pacman.webp" },
    { name: "VAULT", image: "/imagens/vault.webp" },
    { name: "Samus", image: "/imagens/samus.jpg" },
    { name: "Pikmin", image: "/imagens/pikmin.webp" },
    { name: "Doom Slayer", image: "/imagens/doom_slayer.jpg" },
    { name: "Alucard", image: "/imagens/alucard.webp" },
    { name: "Geralt", image: "/imagens/geralt.jpg" },
    { name: "Shovel Knight", image: "/imagens/shovel_knight.jpg" },
    { name: "Kiryu", image: "/imagens/kiyru.webp" },
    { name: "Sackboy", image: "/imagens/sackboy.webp" },
    { name: "Vega", image: "/imagens/vega.jpg" },
    { name: "Waluigi", image: "/imagens/waluigi.png" },
    { name: "Goku", image: "/imagens/goku.jpg" },
    { name: "Luffy", image: "/imagens/luffy.png" },
    { name: "Arthur Morgan", image: "/imagens/arthur_morgan.jpeg" },
    { name: "Yoshi", image: "/imagens/yoshi.png" },
    { name: "Luigi", image: "/imagens/luigi.jpg" },
    { name: "Dante", image: "/imagens/dante.jpg" },
    { name: "Bayonetta", image: "/imagens/bayonetta.jpg" },
    { name: "Fox", image: "/imagens/fox.png" },
    { name: "Ness", image: "/imagens/ness.webp" },
    { name: "Captain Falcon", image: "/imagens/captain_falcom.webp" },
    { name: "Jigglypuff", image: "/imagens/jigglypuff.jpeg" },
    { name: "Peach", image: "/imagens/peach.jpg" },
    { name: "Daisy", image: "/imagens/daisy.png" },
    { name: "Pichu", image: "/imagens/pichu.png" },
    { name: "Falco", image: "/imagens/falco.png" },
    { name: "Ganondorf", image: "/imagens/ganondorf.png" },
    { name: "Wolf", image: "/imagens/wolf.webp" },
    { name: "Villager", image: "/imagens/villager.webp" },
    { name: "Isabelle", image: "/imagens/isabelle.png" },
    { name: "Greninja", image: "/imagens/greninja.webp" },
    { name: "Bowser Jr.", image: "/imagens/bowser_jr.jpg" },
    { name: "Duck Hunt", image: "/imagens/duck_hunt.png" },
    { name: "Ken", image: "/imagens/ken.jpeg" },
    { name: "Cloud", image: "/imagens/cloud.webp" },
    { name: "Inkling", image: "/imagens/inkling.webp" },
    { name: "King K. Rool", image: "/imagens/king_k_rool.jpg" },
    { name: "Incineroar", image: "/imagens/incineroar.jpeg" },
    { name: "Piranha Plant", image: "/imagens/piranha_plant.webp" },
    { name: "Joker", image: "/imagens/joker.jpg" },
    { name: "Silver the Hedgehog", image: "/imagens/silver.webp" },
    { name: "Funky Kong", image: "/imagens/funky_kong.jpeg" },
    { name: "Geno", image: "/imagens/geno.webp" },
    { name: "Shantae", image: "/imagens/shantae.jpeg" },
    { name: "Captain Toad", image: "/imagens/captain_toad.png" },
    { name: "Scorpion (Mortal Kombat)", image: "/imagens/scorpion.png" },
    { name: "Sub Zero (Mortal Kombat)", image: "/imagens/subzero.jpg" },
    { name: "Conker", image: "/imagens/conker.jpg" },
    { name: "Cranky Kong", image: "/imagens/cranky_kong.png" },
    { name: "Diddy Kong", image: "/imagens/diddy_kong.png" },
    { name: "Duke Nukem", image: "/imagens/duke_nukem.webp" },
    { name: "King Dedede", image: "/imagens/king_dedede.webp" },
    { name: "Knuckles", image: "/imagens/knuckles.jpg" },
    { name: "King Boo", image: "/imagens/king_boo.png" },
    { name: "Liu Kang", image: "/imagens/liu_kang.jpg" },
    { name: "Little Mac", image: "/imagens/little_mac.webp" },
    { name: "Papyrus", image: "/imagens/papyrus.jpg" },
    { name: "Shy Guy", image: "/imagens/shy_guy.webp" },
    { name: "Tommy Vercetti", image: "/imagens/tommy_vercetti.webp" },
    { name: "Charizard", image: "/imagens/charizard.jpg" },
    { name: "Eevee", image: "/imagens/eevee.jpg" },
    { name: "Bulbasaur", image: "/imagens/bulbasaur.webp" },
    { name: "Charmander", image: "/imagens/charmander.webp" },
    { name: "Squirtle", image: "/imagens/squirtle.jpeg" },
    { name: "Mew", image: "/imagens/mew.jpg" },
    { name: "Lucario", image: "/imagens/lucario.jpg" },
    { name: "Garchomp", image: "/imagens/garchomp.webp" },
    { name: "Gardevoir", image: "/imagens/gardevoir.webp" },
    { name: "Snorlax", image: "/imagens/snorlax.jpeg" },
    { name: "Blastoise", image: "/imagens/blastoise.jpeg" },
    { name: "Dragonite", image: "/imagens/dragonite.jpg" },
    { name: "Raichu", image: "/imagens/raichu.jpg" },
    { name: "Ditto", image: "/imagens/ditto.webp" },
    { name: "Vaporeon", image: "/imagens/vaporeon.jpeg" },
    { name: "Jolteon", image: "/imagens/jolteon.webp" },
    { name: "Flareon", image: "/imagens/flareon.jpg" },
    { name: "Espeon", image: "/imagens/espeon.webp" },
    { name: "Umbreon", image: "/imagens/umbreon.webp" },
    { name: "Absol", image: "/imagens/absol.webp" },
    { name: "Amy", image: "/imagens/amy.webp" },
    { name: "Rouge", image: "/imagens/rouge.jpg" },
    { name: "Blaze", image: "/imagens/blaze.webp" },
    { name: "Mae", image: "/imagens/mae.webp" },
    { name: "Macaco Engenheiro", image: "/imagens/macaco.webp" },
    { name: "Knight", image: "/imagens/knight.jpeg" },
    { name: "Cortex", image: "/imagens/cortex.jpg" },
    { name: "Coco", image: "/imagens/coco.webp" },
    { name: "Tiny Tiger", image: "/imagens/tinytiger.jpg" },
    { name: "Caneco", image: "/imagens/caneco.webp" },
    { name: "Demonio", image: "/imagens/demonio.webp" },
    { name: "Gris", image: "/imagens/gris.png" },
    { name: "Inside", image: "/imagens/inside.jpg" },
    { name: "Freddy", image: "/imagens/freddy.webp" },
    { name: "Chica", image: "/imagens/chica.webp" },
    { name: "Bonnie", image: "/imagens/bonnie.webp" },
    { name: "Foxy", image: "/imagens/foxy.jpg" },
    { name: "Meta knight", image: "/imagens/meta.jpg" },
    { name: "Klonoa", image: "/imagens/klonoa.jpg" },
    { name: "Ratchet", image: "/imagens/ratchet.jpg" },
    { name: "Lewis", image: "/imagens/lewis.webp" },
    { name: "Robin", image: "/imagens/robin.jpg" },
    { name: "Sebastian", image: "/imagens/sebastian.webp" },
    { name: "Abigail", image: "/imagens/abigail.webp" },
    { name: "Linus", image: "/imagens/linus.jpg" },
    { name: "Leon", image: "/imagens/leon.jpg" },
    { name: "Among us", image: "/imagens/among.webp" },
    { name: "Isaac", image: "/imagens/isac.jpg" },
    { name: "Little Nightmares", image: "/imagens/little.jpg" },
    { name: "Mineirinho", image: "/imagens/mineirinho.jpg" },
    { name: "Mita", image: "/imagens/mita.jpg" },
    { name: "Niko", image: "/imagens/niko.jpg" },
    { name: "Rain world", image: "/imagens/rain.jpg" },
    { name: "Chaim Chomp", image: "/imagens/chaim.webp" },
    { name: "Ghost Pac man", image: "/imagens/ghost.webp" },
    { name: "Guile", image: "/imagens/guile.jpg" },
    { name: "Hammer bro", image: "/imagens/hammer.jpg" },
    { name: "Krystal", image: "/imagens/kristal.webp" },
    { name: "Skull kid", image: "/imagens/skull.jpg" },
    { name: "Piramid Head", image: "/imagens/piramid.webp" },
    { name: "James", image: "/imagens/james.webp" },
    { name: "Toadette", image: "/imagens/toadette.png" },
    { name: "Goomba", image: "/imagens/goomba.jpg" },
    { name: "Koopa", image: "/imagens/koopa.png" },
    { name: "Petey piranha", image: "/imagens/petey.webp" },
    { name: "Kamek", image: "/imagens/kamek.png" },
    { name: "Rosalina", image: "/imagens/rosalina.png" },
    { name: "Pauline", image: "/imagens/pauline.jpg" },
    { name: "Dixie Kong", image: "/imagens/dixie.webp" },
    { name: "Dry Bones", image: "/imagens/dry.jpg" },
    { name: "Birdo", image: "/imagens/birdo.jpg" },
    { name: "Flora", image: "/imagens/flora.webp" },
    { name: "Malt Marzipan", image: "/imagens/fuga.png" },
  ];

export const uniqueCharacters = characters.filter((char, index, self) =>
    index === self.findIndex((c) => c.name === char.name)
);

export const nintendoCharacters = [
    'Mario', 'Link', 'Pikachu', 'Kirby', 'Donkey', 'Bowser', 'Zelda', 'Wario', 'Mewtwo',
    'Yoshi', 'Luigi', 'Peach', 'Daisy', 'Pichu', 'Falco', 'Marth', 'Lucina', 'Ganondorf',
    'Wolf', 'Villager', 'Isabelle', 'Greninja', 'Bowser Jr.', 'Duck Hunt', 'Inkling',
    'Ridley', 'King K. Rool', 'Incineroar', 'Piranha Plant', 'Samus', 'Pikmin', 'Fox',
    'Ness', 'Captain Falcon', 'Jigglypuff', 'Captain Toad', 'Shy Guy', 'King Boo', 'Waluigi',
    'King Dedede', 'Little Mac','Meta Knight', 'Chaim Chomp', 'Hammer Bro', 'Toadette', 'Goomba', 
    'Koopa', 'Petey Piranha', 'Kamek', 'Rosalina', 'Pauline', 'Dixie Kong', 'Dry Bones', 'Birdo',
];
export const anthropomorphicCharacters = [
    'Mewtwo', 'Shadow', 'Spyro', 'Tail', 'Ralsei', 'Yi', 'Laika', 'Pikachu', 'Donkey',
'Banjo', 'Asriel', 'Ori', 'Lamb', 'Crash', 'Bowser', 'Sonic', 'Fox', 'Falco', 'Wolf',
'Greninja', 'Incineroar', 'Funky Kong', 'Diddy Kong', 'Knuckles', 'King K. Rool',
'Isabelle', 'Lucario', 'Eevee', 'Charizard', 'Blastoise', 'Dragonite', 'Raichu', 'Zeraora',
'Sableye', 'Decidueye', 'Lurantis', 'Salamence', 'Garchomp', 'Tyranitar', 'Zygarde', 'Lugia', 'Ho-Oh', 'Amy', 'Rouge',
'Blaze', 'Mae', 'Macaco Engenheiro', 'Coco', 'Tiny Tiger', 'Demonio', 'Freddy', 'Chica', 'Bonnie', 'Foxy', 'Klonoa', 'Ratchet',
'Niko', 'Rain World', 'Hammer Bro', 'Krystal', 'Koopa', 'Petey Piranha', 'Transformice', 'Yoshi', 'Jigglypuff', 
'Pichu', 'Conker', 'Cranky Kong', 'King Dedede', 'Bulbasaur', 'Charmander', 'Squirtle', 
'Mew', 'Gardevoir', 'Snorlax', 'Ditto', 'Vaporeon', 
'Jolteon', 'Flareon', 'Espeon', 'Umbreon', 'Absol', 'Kamek', 'Dixie Kong', 'Birdo', 'Flora', 'Malt Marzipan'

];