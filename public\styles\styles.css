* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --dark-gradient: linear-gradient(135deg, #232526 0%, #414345 100%);
  --neon-blue: #00d4ff;
  --neon-purple: #b537f2;
  --neon-pink: #ff006e;
  --neon-green: #39ff14;
  --glass-bg: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
  --shadow-glow: 0 8px 32px rgba(0, 0, 0, 0.3);
  /* Mobile viewport height fix */
  --vh: 1vh;
}

body {
  background: var(--dark-gradient);
  color: #fff;
  font-family: 'Poppins', 'Segoe UI', Aria<PERSON>, sans-serif;
  margin: 0;
  padding: 0;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  overflow-x: hidden;
  position: relative;
  /* Mobile optimizations */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
  text-size-adjust: 100%;
  /* Prevent zoom on input focus on iOS */
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  /* Enable momentum scrolling on iOS */
  -webkit-overflow-scrolling: touch;
}

body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.15) 0%, transparent 60%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.15) 0%, transparent 60%),
    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.1) 0%, transparent 60%);
  z-index: -2;
  pointer-events: none;
}

h1, h2, h3, h4, h5, h6 {
  font-family: 'Poppins', 'Segoe UI', Arial, sans-serif;
  font-weight: 600;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

header {
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  border-radius: 20px;
  padding: 2rem;
  width: 380px;
  min-width: 340px;
  max-width: 420px;
  min-height: fit-content;
  top: 1rem;
  align-self: flex-start;
  display: flex;
  flex-direction: column;
  gap: 1.2rem;
  overflow-y: auto;
  overflow-x: hidden;
  box-shadow: var(--shadow-glow),
              inset 0 1px 0 rgba(255, 255, 255, 0.1);
  position: relative;
}

header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 50%,
    rgba(255, 255, 255, 0.1) 100%);
  border-radius: 20px;
  z-index: -1;
}

header h1 {
  color: #ffffff;
  font-size: 1.8rem;
  text-align: center;
  margin-bottom: 0.5rem;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
  font-weight: 700;
}

#point-counter {
  font-size: 18px;
  margin: 0.5rem 0;
  align-self: center;
  text-align: center;
  white-space: nowrap;
  min-height: 24px;
  color: #ffffff;
  font-weight: 600;
  font-family: 'Poppins', 'Segoe UI', Arial, sans-serif;
  background: rgba(0, 212, 255, 0.2);
  padding: 0.8rem 1.2rem;
  border-radius: 12px;
  border: 1px solid rgba(0, 212, 255, 0.4);
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
  box-shadow: 0 4px 15px rgba(0, 212, 255, 0.3);
}

main {
  display: none;
  flex-direction: row;
  gap: 2.5rem;
  padding: 1.5rem;
  align-items: flex-start;
  max-width: 1400px;
  margin: 0 auto;
  opacity: 0;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateY(20px);
  width: 100%;
  box-sizing: border-box;
}

main.visible {
  opacity: 1;
  transform: translateY(0);
  animation: fadeInScale 0.6s ease-out;
}

.character-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 1.5rem;
  flex-grow: 1;
  padding: 1rem;
  animation: fadeInScale 0.6s ease-out;
}

.character-grid .character {
  animation: slideInFromTop 0.5s ease-out;
  animation-fill-mode: both;
}

.character-grid .character:nth-child(1) { animation-delay: 0.05s; }
.character-grid .character:nth-child(2) { animation-delay: 0.1s; }
.character-grid .character:nth-child(3) { animation-delay: 0.15s; }
.character-grid .character:nth-child(4) { animation-delay: 0.2s; }
.character-grid .character:nth-child(5) { animation-delay: 0.25s; }
.character-grid .character:nth-child(6) { animation-delay: 0.3s; }
.character-grid .character:nth-child(7) { animation-delay: 0.35s; }
.character-grid .character:nth-child(8) { animation-delay: 0.4s; }
.character-grid .character:nth-child(9) { animation-delay: 0.45s; }
.character-grid .character:nth-child(10) { animation-delay: 0.5s; }

.character {
  cursor: pointer;
  width: 100%;
  overflow: hidden;
  border-radius: 16px;
  border: 2px solid var(--glass-border);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  background: var(--glass-bg);
  backdrop-filter: blur(10px);
  transform: scale(1);
  position: relative;
}

.character::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.1) 0%,
    transparent 50%,
    rgba(255, 255, 255, 0.05) 100%);
  border-radius: 16px;
  z-index: 1;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.character:hover {
  transform: translateY(-6px) scale(1.03);
  border-color: var(--neon-blue);
  box-shadow:
    0 8px 25px rgba(0, 0, 0, 0.25),
    0 0 15px rgba(0, 212, 255, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.character:hover::before {
  opacity: 0.8;
}

.image-container {
  position: relative;
  width: 100%;
  padding-top: 133.33%;
  overflow: hidden;
  border-radius: 10px;
}

.image-container img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 10px;
  transition: transform 0.3s ease;
}

.character:hover .image-container img {
  transform: scale(1.05);
}

.character.selected {
  border-color: var(--neon-pink);
  box-shadow:
    0 0 25px rgba(255, 0, 110, 0.5),
    inset 0 0 20px rgba(255, 0, 110, 0.1);
}

.character.selected .image-container img {
  filter: brightness(40%) saturate(1.2);
}

.character.selected::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 2rem;
  color: var(--neon-pink);
  text-shadow: 0 0 10px rgba(255, 0, 110, 0.8);
  z-index: 10;
  font-weight: bold;
}

#chosenDisplay {
  margin: 0.5rem 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.3rem;
  transition: opacity 0.3s ease;
  opacity: 1;
  max-width: 180px;
  margin: 0 auto;
  position: relative;
}

#chosenDisplay.hidden {
  opacity: 0;
  pointer-events: none;
}

#chosenDisplay h3 {
  margin-bottom: 8px;
  color: #dde2f1;
  font-size: 1.2em;
  text-align: center;
}

#chosenCharacterBox {
  width: 130px;
  height: 172px;
  border: 2px solid #dde2f1;
  border-radius: 12px;
  background: #222;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  box-sizing: border-box;
  position: relative;
  overflow: hidden;
}

#chosenCharacterBox img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 10px;
  display: block;
  margin: 0 auto;
  background: none;
  position: static;
}

.centered-menu {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  height: calc(var(--vh, 1vh) * 100); /* Mobile viewport fix */
  text-align: center;
  position: relative;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 1;
}

.centered-menu h2 {
  font-size: 2.5rem;
  color: #ffffff;
  margin-bottom: 2rem;
  text-shadow: 0 4px 12px rgba(0, 0, 0, 0.6);
  position: relative;
  font-weight: 700;
}

.centered-menu h2::after {
  content: '';
  position: absolute;
  bottom: -15px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 2px;
  background: var(--secondary-gradient);
  border-radius: 2px;
  box-shadow: 0 0 8px rgba(240, 147, 251, 0.3);
  opacity: 0.8;
}

.centered-menu.hidden {
  opacity: 0;
  pointer-events: none;
  transform: scale(0.95);
}

.centered-menu:not(.hidden) {
  animation: fadeInScale 0.5s ease-out;
}

.centered-menu .menu-button {
  animation: slideInFromTop 0.6s ease-out;
  animation-fill-mode: both;
}

.centered-menu .menu-button:nth-child(3) { animation-delay: 0.1s; }
.centered-menu .menu-button:nth-child(4) { animation-delay: 0.2s; }
.centered-menu .menu-button:nth-child(5) { animation-delay: 0.3s; }

.psp-waves {
  position: fixed;
  top: 0;
  left: 0;
  width: 110vw;
  height: 100vh;
  z-index: -1;
  overflow: hidden;
  pointer-events: none;
  opacity: 1;
  transition: opacity 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.psp-waves::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at 30% 70%, rgba(102, 126, 234, 0.08) 0%, transparent 60%),
    radial-gradient(circle at 70% 30%, rgba(181, 55, 242, 0.08) 0%, transparent 60%),
    radial-gradient(circle at 50% 50%, rgba(0, 212, 255, 0.05) 0%, transparent 60%);
  animation: particleFloat 25s ease-in-out infinite;
}

body:not(.game-active) .psp-waves {
  display: block !important;
  opacity: 1 !important;
  pointer-events: auto !important;
}

.wave {
  position: absolute;
  width: 200%;
  height: 70vh;
  bottom: 0;
  left: -50%;
  animation: waveMove 28s linear infinite;
  mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1440 320'%3E%3Cpath fill='%23ffffff' d='M0,192C120,192,240,192,360,197.3C480,203,600,213,720,208C840,203,960,181,1080,176C1200,171,1320,181,1440,181.3L1440,320L0,320Z'%3E%3C/path%3E%3C/svg%3E");
  mask-size: cover;
  mask-repeat: no-repeat;
  mask-position: center;
  transform-origin: bottom center;
  will-change: transform;
  opacity: 1 !important;
  filter: blur(1px);
}

.wave:nth-child(1) {
  animation-duration: 28s;
  animation-delay: 0s;
  opacity: 1 !important;
}

.wave:nth-child(2) {
  animation-duration: 22s;
  animation-delay: -5s;
  opacity: 0.8 !important;
}

.wave:nth-child(3) {
  animation-duration: 16s;
  animation-delay: -10s;
  opacity: 0.6 !important;
}

/* Ajustes para o tema escuro */
body.dark .psp-waves {
  background: var(--dark-gradient);
}

body.dark .wave {
  background: linear-gradient(
    90deg,
    rgba(0, 212, 255, 0) 0%,
    rgba(0, 212, 255, 0.15) 20%,
    rgba(102, 126, 234, 0.2) 50%,
    rgba(181, 55, 242, 0.15) 80%,
    rgba(255, 0, 110, 0) 100%
  );
  filter: brightness(1.2) blur(2px);
}

/* Ajustes para o tema claro */
body.light .psp-waves {
  background: #ffffff;
}

body.light .wave {
  background: linear-gradient(
    90deg,
    rgba(167, 167, 167, 0) 0%,
    rgba(160,160,160,0.10) 20%,
    rgba(160,160,160,0.13) 50%,
    rgba(160,160,160,0.10) 80%,
    rgba(160,160,160,0) 100%
  );
  filter: blur(4px) brightness(1);
}

/* Ajustes para o tema azul */
body.blue .psp-waves {
  background: #1a1f2e;
}

body.blue .wave {
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(224, 231, 255, 0.1) 25%,
    rgba(224, 231, 255, 0.15) 50%,
    rgba(224, 231, 255, 0.1) 75%,
    transparent 100%
  );
}

/* Ajustes para o tema verde */
body.green .psp-waves {
  background: #1a2e1a;
}

body.green .wave {
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(224, 255, 224, 0.1) 25%,
    rgba(224, 255, 224, 0.15) 50%,
    rgba(224, 255, 224, 0.1) 76%,
    transparent 100%
  );
}

/* Ajustes para o tema roxo */
body.purple .psp-waves {
  background: #2e1a2e;
}

body.purple .wave {
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 224, 255, 0.1) 25%,
    rgba(255, 224, 255, 0.15) 50%,
    rgba(255, 224, 255, 0.1) 75%,
    transparent 100%
  );
}

@keyframes waveMove {
  0% {
    transform: translateX(-50%) scale(1);
  }
  50% {
    transform: translateX(0%) scale(1.1);
  }
  100% {
    transform: translateX(-50%) scale(1);
  }
}

@keyframes particleFloat {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.8;
  }
  33% {
    transform: translateY(-20px) rotate(120deg);
    opacity: 1;
  }
  66% {
    transform: translateY(-10px) rotate(240deg);
    opacity: 0.9;
  }
}

@keyframes neonPulse {
  0%, 100% {
    box-shadow:
      0 0 5px currentColor,
      0 0 10px currentColor,
      0 0 15px currentColor;
  }
  50% {
    box-shadow:
      0 0 10px currentColor,
      0 0 20px currentColor,
      0 0 30px currentColor;
  }
}

@keyframes slideInFromTop {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes loadingDots {
  0%, 20% {
    color: var(--neon-blue);
    text-shadow: 0 0 10px rgba(0, 212, 255, 0.8);
  }
  40% {
    color: var(--neon-purple);
    text-shadow: 0 0 10px rgba(181, 55, 242, 0.8);
  }
  60% {
    color: var(--neon-pink);
    text-shadow: 0 0 10px rgba(255, 0, 110, 0.8);
  }
  80%, 100% {
    color: var(--neon-green);
    text-shadow: 0 0 10px rgba(57, 255, 20, 0.8);
  }
}

.loading-text {
  font-size: 1.2rem;
  font-weight: 600;
  text-align: center;
  color: #ffffff;
  animation: loadingDots 2s ease-in-out infinite;
  background: var(--glass-bg);
  backdrop-filter: blur(10px);
  padding: 1rem 2rem;
  border-radius: 12px;
  border: 1px solid var(--glass-border);
  box-shadow: var(--shadow-glow);
  text-shadow: 0 2px 6px rgba(0, 0, 0, 0.5);
}

/* Ajustes para o tema claro */
body.light .centered-menu {
  background: #f5f5f5;

}

body.dark .centered-menu {
  background: #1a1a1a;
}

.menu-button {
  background: var(--glass-bg);
  backdrop-filter: blur(10px);
  color: #ffffff;
  border: 2px solid var(--glass-border);
  padding: 14px 28px;
  margin: 10px;
  border-radius: 16px;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  width: 220px;
  font-weight: 600;
  position: relative;
  overflow: hidden;
  font-family: 'Poppins', 'Segoe UI', Arial, sans-serif;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  text-shadow: 0 2px 6px rgba(0, 0, 0, 0.4);
  box-shadow:
    0 4px 15px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.menu-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent,
    rgba(255, 255, 255, 0.15),
    transparent);
  transition: left 1.2s ease;
}

.menu-button:hover::before {
  left: 100%;
}

.menu-button::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: var(--primary-gradient);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: -1;
  opacity: 0.3;
}

.menu-button:hover {
  transform: translateY(-3px);
  border-color: var(--neon-blue);
  color: #ffffff;
  box-shadow:
    0 8px 25px rgba(0, 0, 0, 0.3),
    0 0 15px rgba(0, 212, 255, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.6);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.menu-button:hover::after {
  width: 280px;
  height: 280px;
  opacity: 0.2;
}

.menu-button:active {
  transform: translateY(-2px) scale(0.98);
}

.button-icon {
  font-size: 1.2em;
  margin-right: 0.5rem;
  display: inline-block;
  transition: transform 0.3s ease;
}

.button-text {
  display: inline-block;
  transition: transform 0.3s ease;
}

.menu-button:hover .button-icon {
  transform: scale(1.05) rotate(3deg);
}

.menu-button:hover .button-text {
  transform: translateX(1px);
}

.customization-group {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.8rem;
  width: 100%;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.customization-group label {
  font-size: 1.1rem;
  font-weight: 600;
  color: #ffffff;
  text-shadow: 0 2px 6px rgba(0, 0, 0, 0.5);
}

.menu-button.small {
  width: auto;
  padding: 8px 16px;
  font-size: 14px;
  margin-top: 10px;
}

/* Estilos para o botão secreto */
.menu-button.secret-button {
  background: var(--secondary-gradient) !important;
  border: 2px solid var(--neon-pink) !important;
  color: #fff !important;
  font-weight: 700;
  box-shadow:
    0 6px 20px rgba(255, 0, 110, 0.4),
    0 0 20px rgba(255, 0, 110, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
  position: relative;
  overflow: hidden;
  animation: neonPulse 2s ease-in-out infinite;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.menu-button.secret-button::before {
  background: linear-gradient(90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent) !important;
}

.menu-button.secret-button:hover {
  border-color: var(--neon-pink) !important;
  box-shadow:
    0 8px 30px rgba(255, 0, 110, 0.6),
    0 0 30px rgba(255, 0, 110, 0.5),
    inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
  transform: translateY(-4px) scale(1.05);
  text-shadow: 0 0 15px rgba(255, 255, 255, 0.8);
}

.menu-button.secret-button:active {
  transform: translateY(-2px) scale(1.02);
}

.menu-button.secret-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
  animation: none;
}

/* Animação de pulse para o botão secreto */
@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 6px 20px rgba(255, 107, 107, 0.5);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);
  }
}



body.light {
  background: linear-gradient(135deg, #f5f5f5 0%, #e8e8e8 100%);
  color: #333;
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --dark-gradient: linear-gradient(135deg, #f5f5f5 0%, #e8e8e8 100%);
  --neon-blue: #667eea;
  --neon-purple: #764ba2;
  --neon-pink: #f5576c;
  --neon-green: #4caf50;
  --glass-bg: rgba(255, 255, 255, 0.9);
  --glass-border: rgba(0, 0, 0, 0.15);
}

body.light .menu-button {
  color: #333 !important;
  text-shadow: 0 1px 3px rgba(255, 255, 255, 0.8) !important;
}

body.light .centered-menu h2 {
  color: #333 !important;
  text-shadow: 0 2px 6px rgba(255, 255, 255, 0.8) !important;
}

body.light header h1 {
  color: #333 !important;
  text-shadow: 0 1px 3px rgba(255, 255, 255, 0.8) !important;
}

body.light #point-counter {
  color: #333 !important;
  text-shadow: 0 1px 3px rgba(255, 255, 255, 0.8) !important;
}

body.light .dropdown-item {
  color: #333 !important;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8) !important;
}

body.light .customization-group label,
body.light #customizationMenu label {
  color: #333 !important;
  text-shadow: 0 1px 3px rgba(255, 255, 255, 0.8) !important;
}

body.light #customizationMenu select {
  color: #333 !important;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8) !important;
}

body.light .scale-value {
  color: #333 !important;
  text-shadow: 0 1px 3px rgba(255, 255, 255, 0.8) !important;
}

body.light .loading-text {
  color: #333 !important;
  text-shadow: 0 1px 3px rgba(255, 255, 255, 0.8) !important;
}

body.dark {
  background-color: #1a1a1a;
  color: #fff;
}

body.light header,
body.light header * {
  color: #333 !important;
  background: transparent !important;
  opacity: 1 !important;
  visibility: visible !important;
  z-index: 100 !important;

}

body.dark header {
  background-color: #2a2a2a;
  color: #fff;
}

body.light .menu-button {
  background-color: #f7f7f7;
  color: #333;
  border: 2px solid #d0d0d0;
}

body.light .menu-button::after {
  background: rgba(0, 0, 0, 0.1);
}

body.dark .menu-button {
  background-color: #2a2a2a;
  color: #fff;
  border: 2px solid #3a3a3a;
}

body.light .menu-button:hover {
  background-color: #eaeaea;
  border-color: #bdbdbd;
}

body.dark .menu-button:hover::after {
  background: rgba(255, 255, 255, 0.2);
}

body.light .character {
  border: 2px solid #e0e0e0;
  background: #ffffff;
}

body.dark .character {
  border: 2px solid #3a3a3a;
  background: #2a2a2a;
}

body.light .character:hover {
  border-color: #d0d0d0;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

body.dark .character:hover {
  border-color: #4a4a4a;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

body.light .dropdown-content {
  background-color: #ffffff;
  border: 2px solid #e0e0e0;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

body.dark .dropdown-content {
  background-color: #2a2a2a;
  border: 2px solid #3a3a3a;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

body.light .dropdown-item {
  color: #333;
}

body.dark .dropdown-item {
  color: #fff;
}

body.light .dropdown-item:hover {
  background-color: #f0f0f0;
  opacity: 1;
}

body.dark .dropdown-item:hover {
  background-color: #3a3a3a;
  opacity: 1;
}

body.light #customizationMenu {
  display: none;
  opacity: 1;
  transition: opacity 0.3s ease;

  h2{
    color: white;
  }
}

body.dark #customizationMenu {
  background: #2a2a2a;
  border: 2px solid #3a3a3a;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

body.light #customizationMenu label {
  color: #e0e0e0;
}

body.dark #customizationMenu label {
  color: #e0e0e0;
}

body.light #customizationMenu select {
  background: #f5f5f5;
  color: #333;
  border: 2px solid #e0e0e0;
}

body.dark #customizationMenu select {
  background: #3a3a3a;
  color: #fff;
  border: 2px solid #4a4a4a;
}

body.light #customizationMenu select:hover {
  background: #e8e8e8;
}

body.dark #customizationMenu select:hover {
  background: #4a4a4a;
}



body.light #point-counter {
  color: #333;
}

body.dark #point-counter {
  color: #e0e0e0;
}

.dropdown {
  position: relative;
  display: inline-block;
  width: 100%;
  overflow-x: hidden;
}

.dropdown button {
  width: 100%;
  text-align: center;
}

.dropdown-content {
  display: none;
  position: absolute;
  background: var(--glass-bg);
  backdrop-filter: blur(15px);
  min-width: 160px;
  box-shadow:
    0 15px 35px rgba(0, 0, 0, 0.3),
    0 0 20px rgba(0, 212, 255, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  z-index: 1000;
  border-radius: 16px;
  padding: 12px;
  width: 100%;
  margin-top: 8px;
  border: 1px solid var(--glass-border);
  max-height: 320px;
  overflow-y: auto;
  overflow-x: hidden;
  position: relative;
  animation: slideInFromTop 0.3s ease-out;
}

/* Remove scrollbar for Chrome, Safari and Opera */
.dropdown-content::-webkit-scrollbar {
  width: 0;
  background: transparent;
}

/* Remove scrollbar for IE, Edge and Firefox */
.dropdown-content {
  -ms-overflow-style: none;
  scrollbar-width: none;
  overflow-y: hidden;
}

.dropdown-content.show {
  display: block;
  overflow-y: hidden;
}

/* Ajuste para o tema claro */
body.light .dropdown-content {
  background-color: #ffffff;
  border: 2px solid #e0e0e0;
}

/* Ajuste para o tema azul */
body.blue .dropdown-content {
  background-color: #2a3447;
  border: 2px solid #3a4b6e;
}

/* Ajuste para o tema verde */
body.green .dropdown-content {
  background-color: #2a472a;
  border: 2px solid #3a6e3a;
}

/* Ajuste para o tema roxo */
body.purple .dropdown-content {
  background-color: #472a47;
  border: 2px solid #6e3a6e;
}

.scroll-indicator {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  opacity: 0.9;
  pointer-events: none;
  transition: opacity 0.3s ease;
}

.scroll-indicator.top {
  top: 0;
  border-bottom: 10px solid #666;
}

.scroll-indicator.bottom {
  bottom: 0;
  border-top: 10px solid #666;
}

/* Ajustes para os temas */
body.dark .scroll-indicator.top {
  border-bottom-color: #666;
}

body.dark .scroll-indicator.bottom {
  border-top-color: #666;
}

body.light .scroll-indicator.top {
  border-bottom-color: #333;
}

body.light .scroll-indicator.bottom {
  border-top-color: #333;
}

body.blue .scroll-indicator.top {
  border-bottom-color: #4a5b8e;
}

body.blue .scroll-indicator.bottom {
  border-top-color: #4a5b8e;
}

body.green .scroll-indicator.top {
  border-bottom-color: #4a8e4a;
}

body.green .scroll-indicator.bottom {
  border-top-color: #4a8e4a;
}

body.purple .scroll-indicator.top {
  border-bottom-color: #8e4a8e;
}

body.purple .scroll-indicator.bottom {
  border-top-color: #8e4a8e;
}

.dropdown-item {
  background: transparent;
  color: #ffffff;
  padding: 12px 18px;
  border: none;
  text-decoration: none;
  display: block;
  cursor: pointer;
  border-radius: 12px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  width: 100%;
  text-align: center;
  margin: 6px 0;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-family: 'Poppins', 'Segoe UI', Arial, sans-serif;
  position: relative;
  border: 1px solid transparent;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
}

.dropdown-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--primary-gradient);
  opacity: 0;
  border-radius: 12px;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.dropdown-item:hover {
  transform: translateY(-1px);
  border-color: var(--neon-blue);
  color: #ffffff;
  box-shadow:
    0 3px 12px rgba(0, 0, 0, 0.15),
    0 0 8px rgba(0, 212, 255, 0.2);
  text-shadow: 0 2px 6px rgba(0, 0, 0, 0.6);
}

.dropdown-item:hover::before {
  opacity: 0.08;
}

.controls {
  display: flex;
  flex-direction: column;
  gap: 12px;
  flex: 1;
  overflow-y: hidden;
  overflow-x: hidden;
  width: 100%;
}

#customizationMenu {
  display: none;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 1.5rem;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 0;
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  padding: 2.5rem;
  border-radius: 20px;
  box-shadow:
    var(--shadow-glow),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  border: 1px solid var(--glass-border);
  width: 350px;
  position: relative;
  z-index: 100;
  animation: fadeInScale 0.4s ease-out;
}

#customizationMenu::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 50%,
    rgba(255, 255, 255, 0.1) 100%);
  border-radius: 20px;
  z-index: -1;
}

#customizationMenu.visible {
  opacity: 1;
  pointer-events: auto;
}

#customizationMenu.hidden {
  opacity: 0;
  pointer-events: none;
}

#customizationMenu label {
  display: block;
  margin-bottom: 0.5rem;
  font-size: 1.1rem;
  color: #ffffff;
  font-weight: 600;
  text-shadow: 0 2px 6px rgba(0, 0, 0, 0.5);
}

#customizationMenu select {
  width: 200px;
  padding: 12px 16px;
  border-radius: 12px;
  background: var(--glass-bg);
  backdrop-filter: blur(10px);
  color: #ffffff;
  border: 2px solid var(--glass-border);
  text-align: center;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-family: 'Poppins', sans-serif;
  text-shadow: 0 2px 6px rgba(0, 0, 0, 0.4);
  box-shadow:
    0 4px 15px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

#customizationMenu select:hover {
  border-color: var(--neon-blue);
  box-shadow:
    0 6px 20px rgba(0, 0, 0, 0.3),
    0 0 15px rgba(0, 212, 255, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

#customizationMenu select:focus {
  outline: none;
  border-color: var(--neon-purple);
  box-shadow:
    0 6px 20px rgba(0, 0, 0, 0.3),
    0 0 20px rgba(181, 55, 242, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}



/* Ajustes responsivos para diferentes escalas */
@media (max-width: 1400px) {
  main {
    padding: 1rem;
    gap: 1.5rem;
  }

  .character-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 1rem;
  }
}

@media (max-width: 1200px) {
  main {
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    padding: 1rem;
    width: 100%;
    max-width: 100vw;
    box-sizing: border-box;
  }

  header {
    position: relative;
    top: 0;
    width: 100%;
    max-width: 500px;
    margin-bottom: 1rem;
  }

  .character-grid {
    grid-template-columns: repeat(2, 1fr);
    width: 100%;
    max-width: 400px;
    gap: 1rem;
    margin: 0 auto;
    justify-content: center;
    align-items: center;
  }
}

/* Mobile-first responsive design improvements */
@media (max-width: 768px) {
  body {
    font-size: 14px;
    overflow-x: hidden;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
  }

  /* Improved character grid for mobile */
  .character-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.8rem;
    padding: 0.8rem;
    width: 100%;
    max-width: 400px;
    margin: 0 auto;
    justify-content: center;
  }

  /* Better header layout for mobile */
  header {
    padding: 1rem;
    border-radius: 12px;
    width: calc(100% - 2rem);
    max-width: none;
    margin: 0.5rem;
    min-width: auto;
  }

  header h1 {
    font-size: 1.4rem;
    margin-bottom: 0.5rem;
  }

  /* Improved chosen character display */
  #chosenDisplay {
    max-width: 120px;
    margin: 0.5rem auto;
  }

  #chosenCharacterBox {
    width: 80px;
    height: 106px;
  }

  /* Better button sizing for touch */
  .menu-button {
    padding: 14px 20px;
    font-size: 14px;
    width: 100%;
    max-width: 280px;
    min-height: 48px; /* Minimum touch target size */
    margin: 8px auto;
    display: block;
    /* Better touch feedback */
    -webkit-tap-highlight-color: rgba(255, 255, 255, 0.1);
    tap-highlight-color: rgba(255, 255, 255, 0.1);
    touch-action: manipulation;
    user-select: none;
    -webkit-user-select: none;
  }

  .menu-button.small {
    padding: 12px 16px;
    font-size: 13px;
    min-height: 44px;
  }

  .menu-button.primary {
    min-height: 52px;
    font-size: 15px;
    font-weight: 600;
  }

  /* Improved point counter */
  #point-counter {
    font-size: 14px;
    padding: 0.8rem 1rem;
    margin: 0.5rem 0;
  }

  /* Better centered menu layout */
  .centered-menu {
    padding: 1rem;
    width: 100%;
    max-width: 100vw;
    box-sizing: border-box;
  }

  .centered-menu h2 {
    font-size: 1.8rem;
    margin-bottom: 1.5rem;
  }

  /* Improved customization menu */
  #customizationMenu {
    width: calc(100% - 2rem);
    max-width: 400px;
    padding: 1.5rem;
    margin: 1rem;
  }

  #customizationMenu h2 {
    font-size: 1.5rem;
  }

  #customizationMenu select,
  #customizationMenu input[type="range"] {
    width: 100%;
    max-width: 100%;
  }

  /* Better start menu layout */
  #startMenu {
    padding: 1rem;
    width: 100%;
    max-width: 100vw;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
  }

  .menu-buttons {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    width: 100%;
    max-width: 320px;
    margin: 0 auto;
    align-items: center;
  }

  .game-logo {
    text-align: center;
    margin-bottom: 1.5rem;
  }

  .game-logo h1 {
    font-size: 2.2rem !important;
    line-height: 1.2;
    margin-bottom: 0.8rem !important;
    text-align: center;
  }

  .game-subtitle {
    font-size: 0.85rem;
    margin-bottom: 1.5rem;
    text-align: center;
    max-width: 300px;
    margin-left: auto;
    margin-right: auto;
  }

  /* Improved tutorial section */
  .tutorial-section {
    padding: 1.5rem !important;
    margin: 1.5rem 0 !important;
    width: 100%;
    box-sizing: border-box;
  }

  .tutorial-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .tutorial-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
  }

  .tutorial-item:hover {
    background: rgba(255, 255, 255, 0.08);
    transform: translateY(-2px);
  }

  .tutorial-icon {
    font-size: 1.5rem;
    flex-shrink: 0;
    margin-top: 0.2rem;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
  }

  .tutorial-text {
    flex: 1;
  }

  .tutorial-text strong {
    display: block;
    margin-bottom: 0.5rem;
    font-size: 1rem;
    color: #ffffff;
    font-weight: 600;
  }

  .tutorial-text p {
    font-size: 0.9rem;
    line-height: 1.4;
    margin: 0;
    color: rgba(255, 255, 255, 0.9);
  }

  /* Better lobby menu */
  #lobbyMenu {
    padding: 1.5rem;
    width: calc(100% - 2rem);
    max-width: 400px;
    margin: 1rem;
  }

  #lobbyMenu h2 {
    font-size: 1.5rem;
  }

  /* Improved button groups */
  .button-group {
    flex-direction: column;
    gap: 0.8rem;
    width: 100%;
  }

  .button-group button {
    width: 100%;
    min-height: 48px;
  }

  /* Better dropdown for mobile */
  .dropdown-content {
    max-height: 60vh;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
  }

  .dropdown-item {
    padding: 16px 20px;
    font-size: 14px;
    min-height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

/* Small mobile devices optimization */
@media (max-width: 480px) {
  body {
    font-size: 13px;
    padding: 0;
    margin: 0;
  }

  /* Two column layout for small screens */
  .character-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.6rem;
    padding: 0.6rem;
    max-width: 350px;
    width: 100%;
    margin: 0 auto;
    justify-content: center;
  }

  /* Compact header for small screens */
  header {
    padding: 0.8rem;
    margin: 0.5rem;
    width: calc(100% - 1rem);
    border-radius: 10px;
  }

  header h1 {
    font-size: 1.2rem;
  }

  /* Smaller chosen character display */
  #chosenDisplay {
    max-width: 100px;
  }

  #chosenCharacterBox {
    width: 70px;
    height: 93px;
  }

  /* Compact buttons for small screens */
  .menu-button {
    padding: 12px 16px;
    font-size: 13px;
    min-height: 44px;
    width: 100%;
    max-width: 100%;
  }

  .menu-button.small {
    padding: 10px 14px;
    font-size: 12px;
    min-height: 40px;
  }

  /* Compact point counter */
  #point-counter {
    font-size: 13px;
    padding: 0.6rem 0.8rem;
  }

  /* Compact start menu */
  #startMenu {
    padding: 0.8rem;
    width: 100%;
  }

  .game-logo h1 {
    font-size: 2rem !important;
    margin-bottom: 0.8rem !important;
  }

  .game-subtitle {
    font-size: 0.8rem;
    margin-bottom: 1rem;
  }

  /* Compact tutorial section */
  .tutorial-section {
    padding: 1rem !important;
    margin: 1rem 0 !important;
  }

  .tutorial-item {
    padding: 0.8rem;
    gap: 0.8rem;
  }

  .tutorial-icon {
    font-size: 1.3rem;
  }

  .tutorial-text strong {
    font-size: 0.9rem;
  }

  .tutorial-text p {
    font-size: 0.8rem;
  }

  /* Compact customization menu */
  #customizationMenu {
    padding: 1rem;
    width: calc(100% - 1rem);
    margin: 0.5rem;
  }

  #customizationMenu h2 {
    font-size: 1.3rem;
  }

  /* Compact lobby menu */
  #lobbyMenu {
    padding: 1rem;
    width: calc(100% - 1rem);
    margin: 0.5rem;
  }

  #lobbyMenu h2 {
    font-size: 1.3rem;
  }

  /* Better form inputs for small screens */
  #lobbyForm input {
    padding: 12px;
    font-size: 14px;
    min-height: 44px;
    border-radius: 8px;
  }

  #lobbyForm label {
    font-size: 1rem;
    margin-bottom: 0.3rem;
  }

  /* Improved dropdown for small screens */
  .dropdown-content {
    max-height: 50vh;
    font-size: 13px;
  }

  .dropdown-item {
    padding: 14px 16px;
    min-height: 44px;
  }

  /* Better character cards for small screens */
  .character {
    border-radius: 12px;
    border-width: 1px;
  }

  .character:hover {
    transform: translateY(-2px) scale(1.02);
  }

  /* Improved settings content */
  .settings-content {
    width: 100%;
    padding: 0;
  }

  .customization-group {
    padding: 0.8rem;
    gap: 0.6rem;
  }

  .customization-group label {
    font-size: 1rem;
  }

  /* Better theme selector */
  .theme-selector {
    padding: 12px;
    font-size: 14px;
    width: 100%;
    border-radius: 8px;
  }

  /* Improved settings header */
  .settings-header {
    margin-bottom: 1rem;
  }

  .settings-header h2 {
    font-size: 1.3rem;
    margin-bottom: 0.5rem;
  }

  .settings-subtitle {
    font-size: 0.9rem;
  }
}

/* Extra small mobile devices (320px and below) */
@media (max-width: 360px) {
  body {
    font-size: 12px;
    padding: 0;
  }

  /* Ultra-compact character grid */
  .character-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
    padding: 0.5rem;
    max-width: 300px;
    width: 100%;
    margin: 0 auto;
    justify-content: center;
  }

  /* Ultra-compact header */
  header {
    padding: 0.6rem;
    margin: 0.3rem;
    width: calc(100% - 0.6rem);
    border-radius: 8px;
  }

  header h1 {
    font-size: 1.1rem;
    margin-bottom: 0.3rem;
  }

  /* Minimal chosen character display */
  #chosenDisplay {
    max-width: 80px;
  }

  #chosenCharacterBox {
    width: 60px;
    height: 80px;
  }

  /* Ultra-compact buttons */
  .menu-button {
    padding: 10px 12px;
    font-size: 12px;
    min-height: 40px;
    width: 100%;
  }

  .menu-button.small {
    padding: 8px 10px;
    font-size: 11px;
    min-height: 36px;
  }

  /* Minimal point counter */
  #point-counter {
    font-size: 12px;
    padding: 0.5rem 0.6rem;
  }

  /* Ultra-compact start menu */
  .game-logo h1 {
    font-size: 1.6rem !important;
    margin-bottom: 0.6rem !important;
    text-align: center;
  }

  .game-subtitle {
    font-size: 0.7rem;
    margin-bottom: 0.8rem;
    text-align: center;
    max-width: 250px;
    margin-left: auto;
    margin-right: auto;
  }

  /* Minimal tutorial section */
  .tutorial-section {
    padding: 0.8rem !important;
    margin: 0.8rem 0 !important;
  }

  .tutorial-item {
    padding: 0.6rem;
    gap: 0.6rem;
  }

  .tutorial-icon {
    font-size: 1.2rem;
  }

  .tutorial-text strong {
    font-size: 0.85rem;
  }

  .tutorial-text p {
    font-size: 0.75rem;
  }

  /* Ultra-compact menus */
  #customizationMenu,
  #lobbyMenu {
    padding: 0.8rem;
    width: calc(100% - 0.6rem);
    margin: 0.3rem;
  }

  #customizationMenu h2,
  #lobbyMenu h2 {
    font-size: 1.2rem;
  }

  /* Minimal form inputs */
  #lobbyForm input {
    padding: 10px;
    font-size: 13px;
    min-height: 40px;
  }

  #lobbyForm label {
    font-size: 0.9rem;
  }

  /* Ultra-compact dropdown */
  .dropdown-item {
    padding: 12px 14px;
    font-size: 12px;
    min-height: 40px;
  }

  /* Minimal settings */
  .customization-group {
    padding: 0.6rem;
  }

  .theme-selector {
    padding: 10px;
    font-size: 13px;
  }
}

/* Ajustes para telas muito grandes */
@media (min-width: 1920px) {
  body {
    font-size: 18px;
  }

  main {
    max-width: 1800px;
  }

  .character-grid {
    grid-template-columns: repeat(6, 1fr);
  }

  header {
    width: 400px;
  }

  #chosenDisplay {
    max-width: 220px;
  }

  #chosenCharacterBox {
    width: 160px;
    height: 213px;
  }

  .menu-button {
    padding: 14px 28px;
    font-size: 18px;
  }

  .menu-button.small {
    padding: 10px 20px;
    font-size: 16px;
  }

  #point-counter {
    font-size: 20px;
  }
}

/* Mobile touch interactions and improvements */
@media (hover: none) and (pointer: coarse) {
  /* Touch-specific styles for mobile devices */
  .menu-button {
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
    touch-action: manipulation;
    user-select: none;
    -webkit-user-select: none;
  }

  .menu-button:active {
    transform: translateY(-1px) scale(0.98);
    transition: transform 0.1s ease;
  }

  .character {
    -webkit-tap-highlight-color: transparent;
    touch-action: manipulation;
    user-select: none;
    -webkit-user-select: none;
  }

  .character:active {
    transform: scale(0.95);
    transition: transform 0.1s ease;
  }

  /* Remove hover effects on touch devices */
  .character:hover {
    transform: none;
    border-color: var(--glass-border);
    box-shadow: none;
  }

  .menu-button:hover {
    transform: none;
    border-color: var(--glass-border);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }

  .dropdown-item:hover {
    transform: none;
    border-color: transparent;
    box-shadow: none;
  }
}

/* Landscape orientation optimizations for mobile */
@media (max-height: 600px) and (orientation: landscape) and (max-width: 1024px) {
  .centered-menu {
    padding: 0.5rem;
    height: auto;
    min-height: 100vh;
    justify-content: flex-start;
    padding-top: 2rem;
  }

  #startMenu,
  #customizationMenu,
  #lobbyMenu {
    padding: 1rem;
    max-height: 90vh;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
  }

  .character-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 0.8rem;
    max-height: 70vh;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    max-width: 500px;
    margin: 0 auto;
  }

  header {
    position: sticky;
    top: 0.5rem;
    z-index: 100;
    backdrop-filter: blur(20px);
    background: rgba(255, 255, 255, 0.95);
  }

  body.dark header {
    background: rgba(26, 26, 26, 0.95);
  }

  .game-logo h1 {
    font-size: 2rem !important;
    margin-bottom: 0.5rem !important;
  }

  .tutorial-section {
    padding: 1rem !important;
    margin: 1rem 0 !important;
  }

  .tutorial-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.8rem;
  }

  .tutorial-item {
    padding: 0.8rem;
  }

  main {
    flex-direction: row;
    gap: 1rem;
    padding: 0.5rem;
  }

  header {
    width: 300px;
    max-width: 300px;
    margin-bottom: 0;
  }
}

/* Ajustes para telas de alta densidade de pixels */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .character img {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* Ajustes para modo escuro do sistema */
@media (prefers-color-scheme: dark) {
  body:not(.light) {
    background-color: #1a1a1a;
    color: #fff;
  }
}

/* Ajustes para modo claro do sistema */
@media (prefers-color-scheme: light) {
  body:not(.dark) {
    background-color: #f5f5f5;
    color: #333;
  }
}

/* Mobile device specific styles */
.mobile-device {
  /* Disable text selection on mobile */
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;

  /* Improve touch scrolling */
  -webkit-overflow-scrolling: touch;

  /* Prevent callouts on iOS */
  -webkit-touch-callout: none;
}

.mobile-device input,
.mobile-device textarea,
.mobile-device select {
  /* Allow text selection in form elements */
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
}

.ios-device {
  /* iOS specific optimizations */
  -webkit-text-size-adjust: 100%;
}

.android-device {
  /* Android specific optimizations */
  text-size-adjust: 100%;
}

/* Improved mobile button interactions */
.mobile-device .menu-button {
  /* Larger touch targets */
  min-height: 48px;
  padding: 14px 20px;

  /* Better touch feedback */
  -webkit-tap-highlight-color: rgba(255, 255, 255, 0.1);
  tap-highlight-color: rgba(255, 255, 255, 0.1);
}

.mobile-device .character {
  /* Better touch feedback for characters */
  -webkit-tap-highlight-color: transparent;
  tap-highlight-color: transparent;
}

/* Prevent zoom on input focus (iOS) */
.mobile-device input[type="text"],
.mobile-device input[type="email"],
.mobile-device input[type="password"],
.mobile-device input[type="number"],
.mobile-device textarea,
.mobile-device select {
  font-size: 16px !important; /* Prevents zoom on iOS */
  transform: translateZ(0); /* Force hardware acceleration */
}

/* Ajustes para reduzir movimento */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

#categoryButton {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #444;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 16px;
  cursor: pointer;
  width: calc(100% - 2px);
  position: relative;
}

.button-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 100%;
}

.button-content span:first-child {
  flex: 1;
  text-align: center;
  white-space: nowrap;
  padding-right: 10px;
}

.arrow {
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 6px solid white;
  transition: transform 0.3s ease;
  margin-left: 10px;
  flex-shrink: 0;
  position: relative;
  z-index: 1;
}

#categoryButton:hover .arrow {
  transform: rotate(180deg);
}

/* Ajustes para os temas */
body.light #categoryButton {
  background-color: #d0d0d0; /* Define um tom de cinza um pouco mais escuro */
  color: #333;
  border: 1px solid #d0d0d0;
}

body.light .arrow {
  border-top-color: #333;
}

body.blue #categoryButton {
  background-color: #3a4b6e;
}

body.green #categoryButton {
  background-color: #3a6e3a;
}

body.purple #categoryButton {
  background-color: #6e3a6e;
}

.character-grid p {
  text-align: center;
  margin: 20px 0;
  min-height: 24px;
  white-space: nowrap;
}

/* Tema Azul */
body.blue {
    background: linear-gradient(135deg, #1a1f2e 0%, #2a3447 100%);
    color: #e0e7ff;
    --primary-gradient: linear-gradient(135deg, #4a5b8e 0%, #667eea 100%);
    --secondary-gradient: linear-gradient(135deg, #667eea 0%, #4a5b8e 100%);
    --neon-blue: #4a5b8e;
    --neon-purple: #667eea;
    --neon-pink: #8e7cc3;
    --neon-green: #7cc3e8;
}

body.blue .header {
    background: #2a3447;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

body.blue .menu-button {
    background-color: #3a4b6e;
    color: #e0e7ff;
    border: 2px solid #4a5b8e;
}

body.blue .menu-button::after {
    background: rgba(224, 231, 255, 0.2);
}

body.blue .menu-button:hover {
    background: #4a5b8e;
}

body.blue .character {
    background: #2a3447;
    border: 1px solid #3a4b6e;
}

body.blue .character:hover {
    border-color: #4a5b8e;
    box-shadow: 0 4px 15px rgba(74, 91, 142, 0.3);
}

body.blue .customization-menu {
    background: #2a3447;
    border: 1px solid #3a4b6e;
}

body.blue select {
    background: #3a4b6e;
    color: #e0e7ff;
}

body.blue select:hover {
    background: #4a5b8e;
}

/* Tema Verde */
body.green {
    background: linear-gradient(135deg, #1a2e1a 0%, #2a472a 100%);
    color: #e0ffe0;
    --primary-gradient: linear-gradient(135deg, #4a8e4a 0%, #66cc66 100%);
    --secondary-gradient: linear-gradient(135deg, #66cc66 0%, #4a8e4a 100%);
    --neon-blue: #4a8e4a;
    --neon-purple: #66cc66;
    --neon-pink: #8ecc8e;
    --neon-green: #39ff14;
}

body.green .header {
    background: #2a472a;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

body.green .menu-button {
    background-color: #3a6e3a;
    color: #e0ffe0;
    border: 2px solid #4a8e4a;
}

body.green .menu-button::after {
    background: rgba(224, 255, 224, 0.2);
}

body.green .menu-button:hover {
    background: #4a8e4a;
}

body.green .character {
    background: #2a472a;
    border: 1px solid #3a6e3a;
}

body.green .character:hover {
    border-color: #4a8e4a;
    box-shadow: 0 4px 15px rgba(74, 142, 74, 0.3);
}

body.green .customization-menu {
    background: #2a472a;
    border: 1px solid #3a6e3a;
}

body.green select {
    background: #3a6e3a;
    color: #e0ffe0;
}

body.green select:hover {
    background: #4a8e4a;
}

/* Tema Roxo */
body.purple {
    background: linear-gradient(135deg, #2e1a2e 0%, #472a47 100%);
    color: #ffe0ff;
    --primary-gradient: linear-gradient(135deg, #8e4a8e 0%, #cc66cc 100%);
    --secondary-gradient: linear-gradient(135deg, #cc66cc 0%, #8e4a8e 100%);
    --neon-blue: #8e4a8e;
    --neon-purple: #b537f2;
    --neon-pink: #ff006e;
    --neon-green: #cc8ecc;
}

body.purple .header {
    background: #472a47;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

body.purple .menu-button {
    background-color: #6e3a6e;
    color: #ffe0ff;
    border: 2px solid #8e4a8e;
}

body.purple .menu-button::after {
    background: rgba(255, 224, 255, 0.2);
}

body.purple .menu-button:hover {
    background: #8e4a8e;
}

body.purple .character {
    background: #472a47;
    border: 1px solid #6e3a6e;
}

body.purple .character:hover {
    border-color: #8e4a8e;
    box-shadow: 0 4px 15px rgba(142, 74, 142, 0.3);
}

body.purple .customization-menu {
    background: #472a47;
    border: 1px solid #6e3a6e;
}

body.purple select {
    background: #6e3a6e;
    color: #ffe0ff;
}

body.purple select:hover {
    background: #8e4a8e;
}

/* Ajustes comuns para todos os temas */
body.blue .point-counter,
body.green .point-counter,
body.purple .point-counter {
    color: #fff;
}

body.blue .character img,
body.green .character img,
body.purple .character img {
    border: 1px solid rgba(255, 255, 255, 0.1);
}

body.blue .character-name,
body.green .character-name,
body.purple .character-name {
    color: #fff;
}

body.blue .character-category,
body.green .character-category,
body.purple .character-category {
    color: rgba(255, 255, 255, 0.7);
}



/* Ajuste responsivo para telas menores */
@media (max-width: 1200px) {
  .character-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

#startMenu {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  transition: opacity 0.5s ease;
  opacity: 1;
}

#startMenu.hidden {
  opacity: 0;
  pointer-events: none;
}

#startMenu.centered-menu {
  background: transparent !important;
}

/* --- INÍCIO DOS ESTILOS MIGRADOS DE main.css --- */
.game-info {
  background: rgba(0, 0, 0, 0.8);
  padding: 16px;
  border-radius: 8px;
  margin: 16px 0;
  color: #fff;
  width: 100%;
}

.game-info .round {
  font-size: 1.2em;
  color: #2a7;
  margin-bottom: 8px;
}

.game-info .target {
  font-size: 1.1em;
  margin-bottom: 8px;
}

.game-info .turn {
  font-size: 1.1em;
  color: #f80;
  margin-bottom: 16px;
}

.game-info .menu-button {
  margin: 8px;
  padding: 8px 16px;
  background: #2a7;
  color: #111 !important;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1em;
  width: 100%;
  font-family: 'Poppins', 'Segoe UI', Arial, sans-serif;
  font-weight: 500;
  transition: background 0.2s, color 0.2s;
}

.game-info .menu-button:hover {
  background: #3b8;
  color: #111 !important;
}

.game-info #resetGameBtn {
  background: #f80;
  color: #111 !important;
}

.game-info #resetGameBtn:hover {
  background: #f90;
  color: #111 !important;
}

.game-info #leaveGameBtn {
  background: #f44;
  color: #111 !important;
}

.game-info #leaveGameBtn:hover {
  background: #f55;
  color: #111 !important;
}

.game-flex-row {
  display: flex;
  flex-direction: row;
  gap: 24px;
  align-items: flex-start;
  margin-top: 8px;
}
@media (max-width: 600px) {
  .game-flex-row {
    flex-direction: column;
    gap: 0;
  }
}
#gameInfo {
  min-width: 220px;
  max-width: 300px;
}
#chosenCharacterHeader {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 16px;
}
#chosenCharacterHeader .chosen-header-img {
  width: 64px;
  height: 64px;
  border-radius: 16px;
  border: 2px solid #dde2f1;
  background: #222;
  object-fit: cover;
  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}
#chosenCharacterHeader .placeholder {
  width: 64px;
  height: 64px;
  border-radius: 16px;
  border: 2px solid #dde2f1;
  background: #222;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #dde2f1;
  font-size: 0.9em;
  opacity: 0.5;
}

.header-flex-row {
  display: flex;
  flex-direction: row;
  gap: 32px;
  align-items: flex-start;
  margin-top: 8px;
}
@media (max-width: 700px) {
  .header-flex-row {
    flex-direction: column;
    gap: 0;
  }
}

body.game-active .psp-waves {
  display: none !important;
}

#lobbyMenu {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 1.5rem;
  transition: opacity 0.5s ease;
  opacity: 1;
  padding: 2rem;
  border-radius: 15px;
  background: #2a2a2a;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
  border: 2px solid #3a3a3a;
  width: 400px;
  max-width: 90%;
}

#lobbyMenu.hidden {
  opacity: 0;
  pointer-events: none;
}

#lobbyMenu h2 {
  color: #e0e0e0;
  font-size: 1.8em;
  margin-bottom: 1rem;
  text-align: center;
}

#lobbyForm {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  width: 100%;
}

#lobbyForm label {
  color: #e0e0e0;
  font-size: 1.1em;
  display: block;
  margin-bottom: 0.5rem;
}

#lobbyForm input {
  width: 100%;
  padding: 12px;
  border-radius: 8px;
  background: #3a3a3a;
  color: #fff;
  border: 2px solid #4a4a4a;
  font-size: 16px; /* Prevent zoom on iOS */
  transition: all 0.3s ease;
  box-sizing: border-box;
  /* Mobile input optimizations */
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  -webkit-tap-highlight-color: transparent;
  touch-action: manipulation;
}

#lobbyForm input:focus {
  outline: none;
  border-color: #5a5a5a;
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.1);
}

#lobbyForm button {
  background-color: #2a2a2a;
  color: #fff;
  border: 2px solid #3a3a3a;
  padding: 12px 24px;
  border-radius: 12px;
  font-size: 1em;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
  font-weight: 500;
  position: relative;
  overflow: hidden;
}

#lobbyForm button::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.6s ease, height 0.6s ease;
}

#lobbyForm button:hover::after {
  width: 300px;
  height: 300px;
}

#lobbyForm button:active {
  transform: scale(0.95);
}

#lobbyStatus {
  color: #e0e0e0;
  text-align: center;
  margin: 1rem 0;
  min-height: 24px;
}

#lobbyPlayers {
  color: #e0e0e0;
  text-align: center;
  margin: 1rem 0;
  min-height: 24px;
}

/* Ajustes para o tema claro */
body.light #lobbyMenu {
  background: #f7f7f7;
  border: 2px solid #d0d0d0;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

body.light #lobbyMenu h2 {
  color: #333;
}

body.light #lobbyForm label {
  color: #333;
}

body.light #lobbyForm input {
  background: #f5f5f5;
  color: #333;
  border: 2px solid #e0e0e0;
}

body.light #lobbyForm input:focus {
  border-color: #d0d0d0;
  box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.1);
}

body.light #lobbyForm button {
  background-color: #f7f7f7;
  color: #333;
  border: 2px solid #d0d0d0;
}

body.light #lobbyForm button::after {
  background: rgba(0, 0, 0, 0.1);
}

body.light #lobbyStatus,
body.light #lobbyPlayers {
  color: #333;
}

/* Ajustes para o tema azul */
body.blue #lobbyMenu {
  background: #2a3447;
  border: 2px solid #3a4b6e;
}

body.blue #lobbyForm input {
  background: #3a4b6e;
  border: 2px solid #4a5b8e;
}

body.blue #lobbyForm button {
  background-color: #3a4b6e;
  border: 2px solid #4a5b8e;
}

/* Ajustes para o tema verde */
body.green #lobbyMenu {
  background: #2a472a;
  border: 2px solid #3a6e3a;
}

body.green #lobbyForm input {
  background: #3a6e3a;
  border: 2px solid #4a8e4a;
}

body.green #lobbyForm button {
  background-color: #3a6e3a;
  border: 2px solid #4a8e4a;
}

/* Ajustes para o tema roxo */
body.purple #lobbyMenu {
  background: #472a47;
  border: 2px solid #6e3a6e;
}

body.purple #lobbyForm input {
  background: #6e3a6e;
  border: 2px solid #8e4a8e;
}

body.purple #lobbyForm button {
  background-color: #6e3a6e;
  border: 2px solid #8e4a8e;
}

.button-group {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
}

.button-group button {
  flex: 1;
}

.lobby-actions {
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
  width: 100%;
  margin-top: 1rem;
}

.lobby-actions .menu-button {
  width: 100%;
}

/* Ajustes para o tema claro */
body.light .button-group button {
  background-color: #f7f7f7;
  color: #333;
  border: 2px solid #d0d0d0;
}

body.light .button-group button:hover {
  background-color: #eaeaea;
  border-color: #bdbdbd;
}

/* Ajustes para o tema azul */
body.blue .button-group button {
  background-color: #3a4b6e;
  border: 2px solid #4a5b8e;
}

body.blue .button-group button:hover {
  background-color: #4a5b8e;
}

/* Ajustes para o tema verde */
body.green .button-group button {
  background-color: #3a6e3a;
  border: 2px solid #4a8e4a;
}

body.green .button-group button:hover {
  background-color: #4a8e4a;
}

/* Ajustes para o tema roxo */
body.purple .button-group button {
  background-color: #6e3a6e;
  border: 2px solid #8e4a8e;
}

body.purple .button-group button:hover {
  background-color: #8e4a8e;
}
