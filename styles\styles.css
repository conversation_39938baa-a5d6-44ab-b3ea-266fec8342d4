* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Melhorias para performance e mobile */
*,
*::before,
*::after {
  box-sizing: border-box;
}

html {
  /* Previne zoom horizontal em iOS */
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
  text-size-adjust: 100%;

  /* Melhora a renderização de fontes */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  /* Scroll suave */
  scroll-behavior: smooth;
}

body {
  background: linear-gradient(135deg, #0f1419 0%, #1a1f2e 50%, #0f1419 100%);
  color: #fff;
  font-family: 'Poppins', 'Segoe UI', Arial, sans-serif;
  margin: 0;
  padding: 0;
  transition: all 0.3s ease;

  /* Melhorias para mobile */
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;

  /* Previne bounce scroll em iOS */
  overscroll-behavior: none;

  /* Melhora performance de scroll */
  -webkit-overflow-scrolling: touch;

  /* Otimização de renderização */
  will-change: transform;
  contain: layout style paint;
}

header {
  background: #2a2a2a;
  border-radius: 12px;
  padding: 1.5rem;
  width: 360px;
  min-width: 320px;
  max-width: 400px;
  height: 800px;
  position: sticky;
  top: 1rem;
  align-self: flex-start;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  overflow-x: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

#point-counter {
  font-size: 18px;
  margin-top: 20px;
  margin-bottom: 20px;
  align-self: flex-start;
  text-align: center;
  white-space: nowrap;
  min-height: 24px;
  color: #e0e0e0;
  font-weight: 500;
}

main {
  display: none;
  flex-direction: row;
  gap: 1.5rem;
  padding: 1.5rem;
  align-items: flex-start;
  max-width: 1400px;
  margin: 0 auto;
  opacity: 0;
  transition: opacity 0.3s ease;
}

main.visible {
  opacity: 1;
}

.character-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 1.2rem;
  flex-grow: 1;
  padding: 0.5rem;
}

.character {
  cursor: pointer;
  width: 100%;
  overflow: hidden;
  border-radius: 12px;
  border: 2px solid #3a3a3a;
  transition: all 0.3s ease;
  background: #2a2a2a;
  transform: scale(1.05);

  /* Melhorias para touch */
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
  min-height: 120px; /* Altura mínima para touch */

  /* Feedback tátil melhorado */
  position: relative;

  /* Otimização de performance */
  will-change: transform, border-color;
  contain: layout style paint;
}

.character:hover {
  transform: translateY(-5px) scale(1.05);
  border-color: #4a4a4a;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.image-container {
  position: relative;
  width: 100%;
  padding-top: 133.33%;
  overflow: hidden;
  border-radius: 10px;
}

.image-container img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 10px;
  transition: transform 0.3s ease;
}

.character:hover .image-container img {
  transform: scale(1.05);
}

.character.selected .image-container img {
  filter: brightness(25%);
}

#chosenCharacterBox {
  position: relative;
  margin-top: 0px;
  border: 2px solid white;
}

#chosenCharacterBox img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.centered-menu {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  text-align: center;
  position: relative;
  overflow: hidden;
  transition: opacity 0.3s ease;
  opacity: 1;
}

.centered-menu.hidden {
  opacity: 0;
  pointer-events: none;
}

.psp-waves {
  position: fixed;
  top: 0;
  left: 0;
  width: 110vw;
  height: 100vh;
  z-index: -1;
  overflow: hidden;
  pointer-events: none;
  opacity: 1;
  transition: opacity 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.wave {
  position: absolute;
  width: 200%;
  height: 70vh;
  bottom: 0;
  left: -50%;
  animation: waveMove 28s linear infinite;
  mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1440 320'%3E%3Cpath fill='%23ffffff' d='M0,192C120,192,240,192,360,197.3C480,203,600,213,720,208C840,203,960,181,1080,176C1200,171,1320,181,1440,181.3L1440,320L0,320Z'%3E%3C/path%3E%3C/svg%3E");
  mask-size: cover;
  mask-repeat: no-repeat;
  mask-position: center;
  transform-origin: bottom center;
  will-change: transform;
  opacity: 1 !important;
}

.wave:nth-child(1) {
  animation-duration: 28s;
  animation-delay: 0s;
  opacity: 1 !important;
}

.wave:nth-child(2) {
  animation-duration: 22s;
  animation-delay: -5s;
  opacity: 0.8 !important;
}

.wave:nth-child(3) {
  animation-duration: 16s;
  animation-delay: -10s;
  opacity: 0.6 !important;
}

/* Ajustes para o tema escuro */
body.dark .psp-waves {
  background: #1a1a1a;
}

body.dark .wave {
  background: linear-gradient(
    90deg,
    rgba(255,255,255,0) 0%,
    rgba(255,255,255,0.18) 20%,
    rgba(255,255,255,0.22) 50%,
    rgba(255,255,255,0.18) 80%,
    rgba(255,255,255,0) 100%
  );
  filter: brightness(1.2) blur(6px);
}

/* Ajustes para o tema claro */
body.light .psp-waves {
  background: #f5f5f5;
}

body.light .wave {
  background: linear-gradient(
    90deg,
    rgba(180,180,180,0) 0%,
    rgba(180,180,180,0.10) 20%,
    rgba(180,180,180,0.13) 50%,
    rgba(180,180,180,0.10) 80%,
    rgba(180,180,180,0) 100%
  );
  filter: blur(4px) brightness(1);
}

/* Ajustes para o tema azul */
body.blue .psp-waves {
  background: #1a1f2e;
}

body.blue .wave {
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(224, 231, 255, 0.1) 25%,
    rgba(224, 231, 255, 0.15) 50%,
    rgba(224, 231, 255, 0.1) 75%,
    transparent 100%
  );
}

/* Ajustes para o tema verde */
body.green .psp-waves {
  background: #1a2e1a;
}

body.green .wave {
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(224, 255, 224, 0.1) 25%,
    rgba(224, 255, 224, 0.15) 50%,
    rgba(224, 255, 224, 0.1) 76%,
    transparent 100%
  );
}

/* Ajustes para o tema roxo */
body.purple .psp-waves {
  background: #2e1a2e;
}

body.purple .wave {
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 224, 255, 0.1) 25%,
    rgba(255, 224, 255, 0.15) 50%,
    rgba(255, 224, 255, 0.1) 75%,
    transparent 100%
  );
}

@keyframes waveMove {
  0% {
    transform: translateX(-50%) scale(1);
    --wave-debug: 'Wave animation at 0%';
  }
  50% {
    transform: translateX(0%) scale(1.1);
    --wave-debug: 'Wave animation at 50%';
  }
  100% {
    transform: translateX(-50%) scale(1);
    --wave-debug: 'Wave animation at 100%';
  }
}

/* Ajustes para o tema claro */
body.light .centered-menu {
  background: #f5f5f5;
}

body.dark .centered-menu {
  background: #1a1a1a;
}

.menu-button {
  background-color: #2a2a2a;
  color: #fff;
  border: 2px solid #3a3a3a;
  padding: 16px 24px; /* Aumentado para melhor touch */
  margin: 8px;
  border-radius: 12px;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 200px;
  min-width: 200px; /* Garante tamanho mínimo */
  min-height: 48px; /* Tamanho mínimo recomendado para touch */
  font-weight: 500;
  position: relative;
  overflow: hidden;

  /* Melhorias para touch */
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;

  /* Feedback visual melhorado */
  transform: translateZ(0);
  backface-visibility: hidden;

  /* Área de toque expandida */
  position: relative;
}

.menu-button::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.6s ease, height 0.6s ease;
}

.menu-button:hover::after {
  width: 300px;
  height: 300px;
}

.menu-button:active {
  transform: scale(0.95);
}

.menu-button.small {
  width: auto;
  padding: 8px 16px;
  font-size: 14px;
  margin-top: 10px;
}

:root {
  --primary-color: #4a90e2;
  --secondary-color: #7b68ee;
  --accent-color: #ff6b6b;
  --success-color: #4caf50;
  --warning-color: #ff9800;
  --nintendo-red: #e60012;
  --nintendo-blue: #0066cc;
}

body.light {
  background-color: #f5f5f5;
  color: #333;
}

body.dark {
  background-color: #1a1a1a;
  color: #fff;
}

body.light header, 
body.light header * {
  color: #333 !important;
  background: transparent !important;
  opacity: 1 !important;
  visibility: visible !important;
  display: block !important;
  z-index: 100 !important;
}

body.dark header {
  background-color: #2a2a2a;
  color: #fff;
}

body.light .menu-button {
  background-color: #f7f7f7;
  color: #333;
  border: 2px solid #d0d0d0;
}

body.light .menu-button::after {
  background: rgba(0, 0, 0, 0.1);
}

body.dark .menu-button {
  background-color: #2a2a2a;
  color: #fff;
  border: 2px solid #3a3a3a;
}

body.light .menu-button:hover {
  background-color: #eaeaea;
  border-color: #bdbdbd;
}

body.dark .menu-button:hover::after {
  background: rgba(255, 255, 255, 0.2);
}

body.light .character {
  border: 2px solid #e0e0e0;
  background: #ffffff;
}

body.dark .character {
  border: 2px solid #3a3a3a;
  background: #2a2a2a;
}

body.light .character:hover {
  border-color: #d0d0d0;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

body.dark .character:hover {
  border-color: #4a4a4a;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

body.light #chosenCharacterBox {
  border: 2px solid #e0e0e0;
  background: #ffffff;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 2px solid gray;
  
}

body.dark #chosenCharacterBox {
  border: 2px solid #3a3a3a;
  background: #2a2a2a;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border: 2px solid white;
}

body.light .dropdown-content {
  background-color: #ffffff;
  border: 2px solid #e0e0e0;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

body.dark .dropdown-content {
  background-color: #2a2a2a;
  border: 2px solid #3a3a3a;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

body.light .dropdown-item {
  color: #333;
}

body.dark .dropdown-item {
  color: #fff;
}

body.light .dropdown-item:hover {
  background-color: #f0f0f0;
}

body.dark .dropdown-item:hover {
  background-color: #3a3a3a;
  opacity: 1;
}

body.light #customizationMenu {
  display: none;
  opacity: 1;
  transition: opacity 0.3s ease;
}

body.dark #customizationMenu {
  background: #2a2a2a;
  border: 2px solid #3a3a3a;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

body.light #customizationMenu label {
  color: #333;
}

body.dark #customizationMenu label {
  color: #e0e0e0;
}

body.light #customizationMenu select {
  background: #f5f5f5;
  color: #333;
  border: 2px solid #e0e0e0;
}

body.dark #customizationMenu select {
  background: #3a3a3a;
  color: #fff;
  border: 2px solid #4a4a4a;
}

body.light #customizationMenu select:hover {
  background: #e8e8e8;
}

body.dark #customizationMenu select:hover {
  background: #4a4a4a;
}

body.light #scaleRange {
  background: #e0e0e0;
}

body.dark #scaleRange {
  background: #3a3a3a;
}

body.light .scale-value {
  color: #666;
}

body.dark .scale-value {
  color: #e0e0e0;
}

body.light #point-counter {
  color: #333;
}

body.dark #point-counter {
  color: #e0e0e0;
}

.dropdown {
  position: relative;
  display: inline-block;
  width: 100%;
}

.dropdown button {
  width: 100%;
  text-align: center;
}

.dropdown-content {
  display: none;
  position: absolute;
  background-color: #2a2a2a;
  min-width: 160px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
  z-index: 1;
  border-radius: 12px;
  padding: 10px;
  width: 100%;
  margin-top: 5px;
  border: 2px solid #3a3a3a;
  max-height: 300px;
  overflow-y: scroll;
  overflow-x: hidden;
  position: relative;
}

/* Remove scrollbar for Chrome, Safari and Opera */
.dropdown-content::-webkit-scrollbar {
  width: 0;
  background: transparent;
}

/* Remove scrollbar for IE, Edge and Firefox */
.dropdown-content {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.dropdown-content.show {
  display: block;
}

/* Ajuste para o tema claro */
body.light .dropdown-content {
  background-color: #ffffff;
  border: 2px solid #e0e0e0;
}

/* Ajuste para o tema azul */
body.blue .dropdown-content {
  background-color: #2a3447;
  border: 2px solid #3a4b6e;
}

/* Ajuste para o tema verde */
body.green .dropdown-content {
  background-color: #2a472a;
  border: 2px solid #3a6e3a;
}

/* Ajuste para o tema roxo */
body.purple .dropdown-content {
  background-color: #472a47;
  border: 2px solid #6e3a6e;
}

.scroll-indicator {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  opacity: 0.9;
  pointer-events: none;
  transition: opacity 0.3s ease;
}

.scroll-indicator.top {
  top: 0;
  border-bottom: 10px solid #666;
}

.scroll-indicator.bottom {
  bottom: 0;
  border-top: 10px solid #666;
}

/* Ajustes para os temas */
body.dark .scroll-indicator.top {
  border-bottom-color: #666;
}

body.dark .scroll-indicator.bottom {
  border-top-color: #666;
}

body.light .scroll-indicator.top {
  border-bottom-color: #333;
}

body.light .scroll-indicator.bottom {
  border-top-color: #333;
}

body.blue .scroll-indicator.top {
  border-bottom-color: #4a5b8e;
}

body.blue .scroll-indicator.bottom {
  border-top-color: #4a5b8e;
}

body.green .scroll-indicator.top {
  border-bottom-color: #4a8e4a;
}

body.green .scroll-indicator.bottom {
  border-top-color: #4a8e4a;
}

body.purple .scroll-indicator.top {
  border-bottom-color: #8e4a8e;
}

body.purple .scroll-indicator.bottom {
  border-top-color: #8e4a8e;
}

.dropdown-item {
  background-color: transparent;
  color: #fff;
  padding: 10px 16px;
  border: none;
  text-decoration: none;
  display: block;
  cursor: pointer;
  border-radius: 8px;
  transition: all 0.3s ease;
  width: 100%;
  text-align: center;
  margin: 4px 0;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.dropdown-item:hover {
  background-color: #3a3a3a;
  transform: translateY(-2px);
}

.controls {
  display: flex;
  flex-direction: column;
  gap: 12px;
  flex: 1;
  overflow-y: hidden;
  overflow-x: hidden;
  width: 100%;
}

#customizationMenu {
  display: none;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  transition: opacity 0.3s ease;
  opacity: 0;
  background: #2a2a2a;
  padding: 2rem;
  border-radius: 15px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
  border: 2px solid #3a3a3a;
  width: 300px;
}

#customizationMenu.visible {
  opacity: 1;
}

#customizationMenu label {
  display: block;
  margin-bottom: 0.5rem;
  font-size: 1.1rem;
  color: #e0e0e0;
  font-weight: 500;
}

#customizationMenu select {
  width: 150px;
  padding: 10px;
  border-radius: 8px;
  background: #3a3a3a;
  color: white;
  border: 2px solid #4a4a4a;
  margin-bottom: 1rem;
  text-align: center;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

#customizationMenu select:hover {
  background: #4a4a4a;
}

#customizationMenu select:focus {
  outline: none;
  border-color: #5a5a5a;
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.1);
}

#scaleRange {
  width: 200px;
  height: 8px;
  -webkit-appearance: none;
  appearance: none;
  background: #3a3a3a;
  outline: none;
  border-radius: 4px;
  margin: 10px auto;
  display: block;
}

#scaleRange::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  background: #fff;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

#scaleRange::-moz-range-thumb {
  width: 20px;
  height: 20px;
  background: #fff;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

#scaleRange::-webkit-slider-thumb:hover {
  background: #e0e0e0;
  transform: scale(1.1);
}

#scaleRange::-moz-range-thumb:hover {
  background: #e0e0e0;
  transform: scale(1.1);
}

.scale-value {
  text-align: center;
  margin-top: 8px;
  font-size: 0.9rem;
  color: #e0e0e0;
  font-weight: 500;
}

/* Ajustes responsivos para diferentes escalas */
@media (max-width: 1400px) {
  main {
    padding: 1rem;
  }
  
  .character-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (max-width: 1200px) {
  main {
    flex-direction: column;
    align-items: center;
  }
  
  header {
    position: relative;
    top: 0;
    width: 100%;
    max-width: 600px;
    height: auto;
    margin-bottom: 1rem;
  }
  
  .character-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 768px) {
  body {
    font-size: 14px;
  }

  .character-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    padding: 1rem;
  }

  .character {
    min-height: 140px; /* Maior para touch em tablets */
  }

  header {
    padding: 1.5rem;
    border-radius: 16px;
    width: 95%;
    max-width: 380px;
    margin: 0 auto;
  }

  #chosenDisplay {
    max-width: 200px;
  }

  #chosenCharacterBox {
    width: 120px;
    height: 160px;
  }

  .menu-button {
    padding: 14px 24px;
    font-size: 15px;
    min-height: 52px;
    width: 220px;
  }

  .menu-button.small {
    padding: 12px 20px;
    font-size: 14px;
    min-height: 48px;
  }

  #point-counter {
    font-size: 16px;
    padding: 0.8rem 1rem;
  }

  .centered-menu h2 {
    font-size: 2rem;
    margin-bottom: 1.5rem;
  }

  #customizationMenu {
    width: 90%;
    max-width: 380px;
    padding: 2rem;
  }

  /* Melhorias para o menu inicial */
  #startMenu {
    padding: 1.5rem;
    width: 90%;
    max-width: 400px;
  }

  #startMenu h2 {
    font-size: 1.8em;
    margin-bottom: 1rem;
  }

  /* Melhorias para formulários */
  input[type="text"],
  input[type="email"],
  select {
    padding: 12px 16px;
    font-size: 16px; /* Previne zoom em iOS */
    min-height: 48px;
    border-radius: 8px;
    border: 2px solid #3a3a3a;
    background: #2a2a2a;
    color: #fff;
    width: 100%;
    box-sizing: border-box;
  }

  /* Melhorias para dropdown */
  .dropdown-content {
    max-height: 250px;
    border-radius: 8px;
  }

  .dropdown-item {
    padding: 12px 16px;
    font-size: 15px;
    min-height: 48px;
  }
}

@media (max-width: 480px) {
  body {
    font-size: 13px;
  }

  .character-grid {
    grid-template-columns: repeat(3, 1fr); /* 3 colunas para smartphones */
    gap: 0.6rem;
    padding: 0.6rem;
    max-width: 100%;
  }

  .character {
    min-height: 100px; /* Otimizado para smartphones */
    padding: 0.3rem;
  }

  .character img {
    width: 100%;
    height: auto;
    max-height: 70px;
    object-fit: cover;
  }

  .character-name {
    font-size: 0.7rem;
    margin-top: 0.2rem;
  }

  .character-category {
    font-size: 0.6rem;
  }

  header {
    padding: 1rem;
    width: 95%;
    max-width: 100%;
    border-radius: 12px;
  }

  #chosenDisplay {
    max-width: 150px;
  }

  #chosenCharacterBox {
    width: 100px;
    height: 133px;
  }

  .menu-button {
    padding: 12px 20px;
    font-size: 14px;
    min-height: 50px;
    width: 100%;
    max-width: 280px;
    margin: 6px auto;
  }

  .menu-button.small {
    padding: 10px 16px;
    font-size: 13px;
    min-height: 46px;
  }

  #point-counter {
    font-size: 14px;
    padding: 0.6rem 1rem;
  }

  /* Melhorias para o menu inicial */
  #startMenu {
    padding: 1rem;
    width: 95%;
    max-width: 100%;
  }

  #startMenu h2 {
    font-size: 1.6em;
    margin-bottom: 1rem;
  }

  /* Melhorias para o menu de customização */
  #customizationMenu {
    padding: 1.5rem;
    width: 95%;
    max-width: 100%;
  }

  #customizationMenu h2 {
    font-size: 1.5em;
  }

  /* Melhorias para o lobby */
  #lobbyMenu {
    padding: 1rem;
    width: 95%;
    max-width: 100%;
  }

  #lobbyMenu h2 {
    font-size: 1.5em;
  }

  #lobbyForm input {
    padding: 12px 16px;
    font-size: 16px; /* Previne zoom em iOS */
    min-height: 50px;
  }

  /* Melhorias para formulários */
  input[type="text"],
  input[type="email"],
  select {
    font-size: 16px; /* Importante para iOS */
    padding: 14px 16px;
    min-height: 50px;
  }

  /* Melhorias para dropdown */
  .dropdown-content {
    max-height: 200px;
  }

  .dropdown-item {
    padding: 14px 16px;
    font-size: 14px;
    min-height: 50px;
  }

  /* Melhorias para orientação */
  main {
    padding: 0.8rem;
  }

  /* Ajustes para teclado virtual */
  .centered-menu {
    min-height: 100vh;
    min-height: 100dvh; /* Suporte para dynamic viewport */
  }
}

#categoryButton {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #444;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 16px;
  cursor: pointer;
  width: calc(100% - 2px);
  position: relative;
}

.button-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 100%;
}

.button-content span:first-child {
  flex: 1;
  text-align: center;
  white-space: nowrap;
  padding-right: 10px;
}

.arrow {
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 6px solid white;
  transition: transform 0.3s ease;
  margin-left: 10px;
  flex-shrink: 0;
  position: relative;
  z-index: 1;
}

#categoryButton:hover .arrow {
  transform: rotate(180deg);
}

/* Ajustes para os temas */
body.light #categoryButton {
  background-color: #e0e0e0;
  color: #333;
}

body.light .arrow {
  border-top-color: #333;
}

body.blue #categoryButton {
  background-color: #3a4b6e;
}

body.green #categoryButton {
  background-color: #3a6e3a;
}

body.purple #categoryButton {
  background-color: #6e3a6e;
}

.character-grid p {
  text-align: center;
  margin: 20px 0;
  min-height: 24px;
  white-space: nowrap;
}

/* Tema Azul */
body.blue {
    background: #1a1f2e;
    color: #e0e7ff;
}

body.blue .header {
    background: #2a3447;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

body.blue .menu-button {
    background-color: #3a4b6e;
    color: #e0e7ff;
    border: 2px solid #4a5b8e;
}

body.blue .menu-button::after {
    background: rgba(224, 231, 255, 0.2);
}

body.blue .menu-button:hover {
    background: #4a5b8e;
}

body.blue .character {
    background: #2a3447;
    border: 1px solid #3a4b6e;
}

body.blue .character:hover {
    border-color: #4a5b8e;
    box-shadow: 0 4px 15px rgba(74, 91, 142, 0.3);
}

body.blue .customization-menu {
    background: #2a3447;
    border: 1px solid #3a4b6e;
}

body.blue select {
    background: #3a4b6e;
    color: #e0e7ff;
}

body.blue select:hover {
    background: #4a5b8e;
}

/* Tema Verde */
body.green {
    background: #1a2e1a;
    color: #e0ffe0;
}

body.green .header {
    background: #2a472a;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

body.green .menu-button {
    background-color: #3a6e3a;
    color: #e0ffe0;
    border: 2px solid #4a8e4a;
}

body.green .menu-button::after {
    background: rgba(224, 255, 224, 0.2);
}

body.green .menu-button:hover {
    background: #4a8e4a;
}

body.green .character {
    background: #2a472a;
    border: 1px solid #3a6e3a;
}

body.green .character:hover {
    border-color: #4a8e4a;
    box-shadow: 0 4px 15px rgba(74, 142, 74, 0.3);
}

body.green .customization-menu {
    background: #2a472a;
    border: 1px solid #3a6e3a;
}

body.green select {
    background: #3a6e3a;
    color: #e0ffe0;
}

body.green select:hover {
    background: #4a8e4a;
}

/* Tema Roxo */
body.purple {
    background: #2e1a2e;
    color: #ffe0ff;
}

body.purple .header {
    background: #472a47;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

body.purple .menu-button {
    background-color: #6e3a6e;
    color: #ffe0ff;
    border: 2px solid #8e4a8e;
}

body.purple .menu-button::after {
    background: rgba(255, 224, 255, 0.2);
}

body.purple .menu-button:hover {
    background: #8e4a8e;
}

body.purple .character {
    background: #472a47;
    border: 1px solid #6e3a6e;
}

body.purple .character:hover {
    border-color: #8e4a8e;
    box-shadow: 0 4px 15px rgba(142, 74, 142, 0.3);
}

body.purple .customization-menu {
    background: #472a47;
    border: 1px solid #6e3a6e;
}

body.purple select {
    background: #6e3a6e;
    color: #ffe0ff;
}

body.purple select:hover {
    background: #8e4a8e;
}

/* Ajustes comuns para todos os temas */
body.blue .point-counter,
body.green .point-counter,
body.purple .point-counter {
    color: #fff;
}

body.blue .character img,
body.green .character img,
body.purple .character img {
    border: 1px solid rgba(255, 255, 255, 0.1);
}

body.blue .character-name,
body.green .character-name,
body.purple .character-name {
    color: #fff;
}

body.blue .character-category,
body.green .character-category,
body.purple .character-category {
    color: rgba(255, 255, 255, 0.7);
}

/* Ajustes do slider para os novos temas */
body.blue #scaleRange::-webkit-slider-thumb {
    background: #4a5b8e;
}

body.green #scaleRange::-webkit-slider-thumb {
    background: #4a8e4a;
}

body.purple #scaleRange::-webkit-slider-thumb {
    background: #8e4a8e;
}

body.blue #scaleRange::-moz-range-thumb {
    background: #4a5b8e;
}

body.green #scaleRange::-moz-range-thumb {
    background: #4a8e4a;
}

body.purple #scaleRange::-moz-range-thumb {
    background: #8e4a8e;
}

/* Melhorias para tablets e telas médias */
@media (max-width: 1200px) and (min-width: 769px) {
  .character-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 1rem;
  }

  main {
    padding: 1rem;
    gap: 1.5rem;
  }

  header {
    width: 320px;
    max-width: 350px;
  }

  .tutorial-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.2rem;
  }

  .menu-buttons {
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: center;
    gap: 1rem;
  }

  .menu-button {
    min-width: 200px;
    max-width: 250px;
  }
}

/* Melhorias para telas grandes (desktop) */
@media (min-width: 1400px) {
  .character-grid {
    grid-template-columns: repeat(6, 1fr);
    gap: 1.5rem;
  }

  main {
    max-width: 1600px;
    padding: 2rem;
  }

  header {
    width: 400px;
    max-width: 450px;
  }

  .tutorial-grid {
    grid-template-columns: repeat(3, 1fr);
    max-width: 1200px;
  }
}

/* Melhorias para telas ultra-wide */
@media (min-width: 1800px) {
  .character-grid {
    grid-template-columns: repeat(8, 1fr);
  }

  main {
    max-width: 2000px;
  }

  .tutorial-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

#chosenDisplay {
  margin-top: 0.5rem;
  margin-bottom: 1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.3rem;
  transition: opacity 0.3s ease;
  opacity: 1;
  max-width: 180px;
  margin: 0 auto;
  position:sticky;
}

#chosenDisplay.hidden {
  opacity: 0;
  pointer-events: none;
}

#startMenu {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  transition: opacity 0.5s ease;
  opacity: 1;
}

#startMenu.hidden {
  opacity: 0;
  pointer-events: none;
}

#startMenu.centered-menu {
  background: transparent !important;
}

/* Otimizações específicas para dispositivos móveis */
.mobile-device {
  /* Melhora performance em mobile */
  -webkit-transform: translateZ(0);
  transform: translateZ(0);

  /* Otimiza renderização */
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;

  /* Reduz repaints */
  -webkit-perspective: 1000;
  perspective: 1000;
}

.mobile-device .character-grid {
  /* Otimiza scroll em mobile */
  -webkit-overflow-scrolling: touch;
  overflow-scrolling: touch;
}

.mobile-device .character {
  /* Melhora responsividade touch */
  -webkit-tap-highlight-color: rgba(255, 255, 255, 0.1);
  tap-highlight-color: rgba(255, 255, 255, 0.1);
}

.mobile-device .menu-button {
  /* Feedback visual melhorado para touch */
  -webkit-tap-highlight-color: rgba(255, 255, 255, 0.1);
  tap-highlight-color: rgba(255, 255, 255, 0.1);
}

/* Suporte para viewport dinâmico */
.centered-menu {
  height: 100vh;
  height: calc(var(--vh, 1vh) * 100);
}

/* Lazy loading placeholder */
.lazy {
  opacity: 0.6;
  transition: opacity 0.3s ease;
}

.lazy.loaded {
  opacity: 1;
}

/* Melhorias para PWA */
@media (display-mode: standalone) {
  body {
    /* Remove margens quando executado como PWA */
    margin: 0;
    padding: 0;
  }

  .centered-menu {
    /* Ajusta altura para PWA */
    height: 100vh;
    height: 100dvh;
  }
}

/* Otimizações para telas de alta densidade */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .character img,
  #chosenCharacterBox img {
    /* Melhora qualidade em telas retina */
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
    image-rendering: pixelated;
  }
}

/* Melhorias para acessibilidade em mobile */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  .wave {
    animation: none !important;
  }
}

/* Ajustes para modo de alto contraste */
@media (prefers-contrast: high) {
  .character {
    border-width: 3px;
  }

  .menu-button {
    border-width: 3px;
    font-weight: 600;
  }
}

/* Otimizações para economia de bateria */
@media (prefers-reduced-data: reduce) {
  .wave {
    display: none;
  }

  .character img {
    /* Reduz qualidade para economizar dados */
    image-rendering: optimizeSpeed;
  }
}

/* ===== MELHORIAS ESPECÍFICAS PARA DISPOSITIVOS ===== */

/* Otimizações para iOS */
.ios-device {
  /* Prevenir bounce scroll */
  overscroll-behavior: none;
  -webkit-overflow-scrolling: touch;
}

.ios-device input[type="text"],
.ios-device input[type="email"],
.ios-device select {
  /* Prevenir zoom automático em iOS */
  font-size: 16px !important;
  transform: translateZ(0);
  -webkit-appearance: none;
}

.ios-device .menu-button {
  /* Melhorar performance de toque em iOS */
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
}

/* Otimizações para Android */
.android-device {
  /* Melhorar performance de animações */
  will-change: transform;
}

.android-device .character {
  /* Otimizar renderização em Android */
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* Otimizações para tablets */
.tablet-device .character-grid {
  grid-template-columns: repeat(4, 1fr);
  gap: 1.2rem;
}

.tablet-device .menu-button {
  min-width: 220px;
  padding: 1.2rem 2rem;
  font-size: 1.1rem;
}

/* Otimizações por tamanho de tela */
.device-tiny .character-grid {
  grid-template-columns: repeat(2, 1fr) !important;
  gap: 0.4rem !important;
}

.device-small .character-grid {
  grid-template-columns: repeat(3, 1fr) !important;
  gap: 0.6rem !important;
}

.device-medium .character-grid {
  grid-template-columns: repeat(3, 1fr) !important;
  gap: 0.8rem !important;
}

.device-large .character-grid {
  grid-template-columns: repeat(4, 1fr) !important;
  gap: 1rem !important;
}

.device-xlarge .character-grid {
  grid-template-columns: repeat(5, 1fr) !important;
  gap: 1.2rem !important;
}

/* Melhorias de acessibilidade */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  .wave {
    animation: none !important;
  }

  .character:hover {
    transform: none !important;
  }

  .menu-button:hover {
    transform: none !important;
  }
}

@media (prefers-contrast: high) {
  .character {
    border-width: 3px !important;
    border-color: #fff !important;
  }

  .menu-button {
    border-width: 3px !important;
    font-weight: 700 !important;
  }

  .tutorial-item {
    border-width: 2px !important;
    border-color: rgba(255, 255, 255, 0.5) !important;
  }
}

/* Melhorias para modo escuro automático */
@media (prefers-color-scheme: dark) {
  :root {
    --primary-color: #4a90e2;
    --secondary-color: #7b68ee;
    --accent-color: #ff6b6b;
    --bg-color: #1a1a1a;
    --text-color: #ffffff;
  }
}

@media (prefers-color-scheme: light) {
  :root {
    --primary-color: #2563eb;
    --secondary-color: #7c3aed;
    --accent-color: #dc2626;
    --bg-color: #f5f5f5;
    --text-color: #1f2937;
  }
}

/* ===== NOVOS ESTILOS PARA CHARACTER CLASH ===== */

/* Logo do jogo */
.game-logo {
  margin-bottom: 2rem;
  text-align: center;
}

.game-logo h1 {
  font-size: 3.5rem;
  font-weight: 800;
  background: linear-gradient(45deg, var(--primary-color), var(--secondary-color), var(--accent-color));
  background-size: 200% 200%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: gradientShift 3s ease-in-out infinite;
  margin: 0;
  text-shadow: 0 0 30px rgba(74, 144, 226, 0.3);
  letter-spacing: 2px;
}

.game-subtitle {
  font-size: 1.1rem;
  color: #b0b0b0;
  margin: 0.5rem 0 0 0;
  font-weight: 300;
  letter-spacing: 1px;
}

@keyframes gradientShift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

/* Seção do tutorial */
.tutorial-section {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  padding: 2rem;
  margin: 2rem 0;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.tutorial-section h3 {
  text-align: center;
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
  color: var(--primary-color);
  font-weight: 600;
}

.tutorial-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  max-width: 900px;
  margin: 0 auto;
}

.tutorial-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.08);
  transition: all 0.3s ease;
}

.tutorial-item:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.tutorial-icon {
  font-size: 2rem;
  flex-shrink: 0;
  filter: drop-shadow(0 0 8px rgba(74, 144, 226, 0.3));
}

.tutorial-text strong {
  display: block;
  color: #fff;
  font-size: 1.1rem;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.tutorial-text p {
  color: #b0b0b0;
  margin: 0;
  font-size: 0.9rem;
  line-height: 1.4;
}

/* Botões do menu */
.menu-buttons {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: center;
  margin-top: 2rem;
}

.menu-button.primary {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  border: none;
  color: white;
  font-weight: 600;
  font-size: 1.1rem;
  padding: 1rem 2rem;
  min-width: 250px;
  box-shadow: 0 4px 16px rgba(74, 144, 226, 0.3);
  position: relative;
  overflow: hidden;
}

.menu-button.primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.menu-button.primary:hover::before {
  left: 100%;
}

.menu-button.primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(74, 144, 226, 0.4);
}

.menu-button.secondary {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  color: #b0b0b0;
  font-weight: 500;
  min-width: 200px;
}

.menu-button.secondary:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
  color: #fff;
}

/* Configurações */
.settings-header {
  text-align: center;
  margin-bottom: 2rem;
}

.settings-header h2 {
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
  color: var(--primary-color);
}

.settings-subtitle {
  color: #b0b0b0;
  font-size: 1rem;
  margin: 0;
}

.settings-content {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  padding: 2rem;
  margin-bottom: 2rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.customization-group {
  margin-bottom: 1.5rem;
}

.customization-group label {
  display: block;
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.8rem;
  color: #fff;
}

.theme-selector {
  width: 100%;
  padding: 0.8rem 1rem;
  border-radius: 8px;
  border: 2px solid rgba(255, 255, 255, 0.2);
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.theme-selector:hover {
  border-color: var(--primary-color);
  background: rgba(255, 255, 255, 0.15);
}

.theme-selector:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.2);
}

.setting-description {
  color: #b0b0b0;
  font-size: 0.9rem;
  margin: 0.5rem 0 0 0;
  font-style: italic;
}

/* Responsividade para mobile */
@media (max-width: 768px) {
  .centered-menu {
    padding: 1rem;
    max-height: 100vh;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
  }

  .game-logo h1 {
    font-size: 2.2rem;
    margin-bottom: 0.5rem;
  }

  .game-subtitle {
    font-size: 0.9rem;
    margin-bottom: 1.5rem;
  }

  .tutorial-section {
    padding: 1rem;
    margin: 1rem 0;
  }

  .tutorial-section h3 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
  }

  .tutorial-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
    max-width: 100%;
  }

  .tutorial-item {
    padding: 1rem;
    flex-direction: column;
    text-align: center;
  }

  .tutorial-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
  }

  .tutorial-text {
    margin-left: 0;
  }

  .tutorial-text strong {
    font-size: 1rem;
  }

  .tutorial-text p {
    font-size: 0.85rem;
    line-height: 1.4;
  }

  .menu-buttons {
    flex-direction: column;
    gap: 1rem;
    margin-top: 1.5rem;
  }

  .menu-button {
    width: 100%;
    max-width: 280px;
    padding: 1rem 1.5rem;
    font-size: 1rem;
  }

  .button-icon {
    font-size: 1.2rem;
  }
}

/* Responsividade para tablets */
@media (max-width: 1024px) and (min-width: 769px) {
  .tutorial-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.2rem;
  }

  .menu-buttons {
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: center;
    gap: 1rem;
  }

  .menu-button {
    min-width: 200px;
    max-width: 250px;
  }
}

/* Responsividade para telas muito pequenas */
@media (max-width: 480px) {
  .centered-menu {
    padding: 0.5rem;
  }

  .game-logo h1 {
    font-size: 1.8rem;
  }

  .game-subtitle {
    font-size: 0.8rem;
  }

  .tutorial-section {
    padding: 0.8rem;
  }

  .tutorial-item {
    padding: 0.8rem;
  }

  .tutorial-text strong {
    font-size: 0.9rem;
  }

  .tutorial-text p {
    font-size: 0.8rem;
  }

  .menu-button {
    max-width: 100%;
    padding: 0.8rem 1.2rem;
    font-size: 0.9rem;
  }
}

/* Melhorias para orientação landscape em mobile - VERSÃO MELHORADA */
@media (max-height: 600px) and (orientation: landscape) {
  .centered-menu {
    padding: 0.5rem !important;
    justify-content: center !important;
    min-height: 100vh !important;
    min-height: 100dvh !important;
  }

  .game-logo {
    margin-bottom: 1rem !important;
  }

  .game-logo h1 {
    font-size: 1.8rem !important;
    margin-bottom: 0.3rem !important;
    line-height: 1.1 !important;
  }

  .game-subtitle {
    font-size: 0.8rem !important;
    margin-bottom: 1rem !important;
  }

  .tutorial-section {
    padding: 0.8rem !important;
    margin: 0.8rem 0 !important;
  }

  .tutorial-section h3 {
    font-size: 1.2rem !important;
    margin-bottom: 0.8rem !important;
  }

  .tutorial-grid {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 0.8rem !important;
  }

  .tutorial-item {
    padding: 0.8rem !important;
    flex-direction: row !important;
    text-align: left !important;
    gap: 0.8rem !important;
  }

  .tutorial-icon {
    font-size: 1.5rem !important;
    margin-bottom: 0 !important;
    flex-shrink: 0 !important;
  }

  .tutorial-text {
    text-align: left !important;
  }

  .tutorial-text strong {
    font-size: 0.9rem !important;
  }

  .tutorial-text p {
    font-size: 0.8rem !important;
  }

  .menu-buttons {
    margin-top: 1rem !important;
    flex-direction: row !important;
    flex-wrap: wrap !important;
    justify-content: center !important;
    gap: 0.8rem !important;
  }

  .menu-button {
    max-width: 180px !important;
    min-width: 140px !important;
    padding: 0.8rem 1rem !important;
    font-size: 0.9rem !important;
    min-height: 44px !important;
  }

  /* Layout do jogo em landscape */
  main {
    flex-direction: row !important;
    align-items: flex-start !important;
    gap: 1rem !important;
    padding: 0.5rem !important;
  }

  header {
    width: 300px !important;
    max-width: 300px !important;
    height: auto !important;
    max-height: calc(100vh - 1rem) !important;
    overflow-y: auto !important;
    position: sticky !important;
    top: 0.5rem !important;
  }

  .character-grid {
    grid-template-columns: repeat(4, 1fr) !important;
    gap: 0.6rem !important;
  }

  .character {
    min-height: 100px !important;
  }
}

/* Melhorias específicas para o jogo em landscape mobile */
@media (max-height: 500px) and (orientation: landscape) {
  main {
    padding: 0.5rem;
  }

  header {
    padding: 0.5rem;
    margin-bottom: 0.5rem;
  }

  .character-grid {
    grid-template-columns: repeat(6, 1fr);
    gap: 0.4rem;
    padding: 0.4rem;
  }

  .character {
    min-height: 80px;
    padding: 0.2rem;
  }

  .character img {
    max-height: 50px;
  }

  .character-name {
    font-size: 0.6rem;
  }

  .character-category {
    font-size: 0.5rem;
  }

  #point-counter {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
  }

  .menu-button.small {
    padding: 0.4rem 0.8rem;
    font-size: 0.7rem;
    min-height: 36px;
  }
}

/* Melhorias para telas ultra pequenas - VERSÃO MELHORADA */
@media (max-width: 360px) {
  .character-grid {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 0.6rem !important;
    padding: 0.5rem !important;
  }

  .character {
    min-height: 110px !important;
    padding: 0.4rem !important;
  }

  .character img {
    max-height: 70px !important;
  }

  .game-logo h1 {
    font-size: 1.8rem !important;
    letter-spacing: 1px !important;
  }

  .game-subtitle {
    font-size: 0.8rem !important;
  }

  .tutorial-section {
    padding: 0.8rem !important;
  }

  .tutorial-text strong {
    font-size: 0.9rem !important;
  }

  .tutorial-text p {
    font-size: 0.8rem !important;
  }

  .menu-button {
    padding: 0.8rem 1.2rem !important;
    font-size: 0.9rem !important;
    min-height: 48px !important;
  }

  .menu-button.primary {
    font-size: 1rem !important;
  }

  /* Melhorias para formulários em telas pequenas */
  input[type="text"],
  input[type="email"],
  select {
    font-size: 16px !important;
    padding: 14px 12px !important;
  }

  /* Header mais compacto */
  header {
    padding: 0.8rem !important;
  }

  #point-counter {
    font-size: 14px !important;
    padding: 0.6rem 0.8rem !important;
  }

  .menu-button.small {
    padding: 8px 12px !important;
    font-size: 12px !important;
    min-height: 40px !important;
  }
}

/* Melhorias para telas muito pequenas (< 320px) */
@media (max-width: 320px) {
  .game-logo h1 {
    font-size: 1.6rem !important;
  }

  .character-grid {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 0.4rem !important;
  }

  .character {
    min-height: 100px !important;
    padding: 0.3rem !important;
  }

  .tutorial-item {
    padding: 0.6rem !important;
  }

  .menu-button {
    padding: 0.7rem 1rem !important;
    font-size: 0.85rem !important;
    min-height: 44px !important;
  }
}

/* CORREÇÕES CRÍTICAS PARA MOBILE - VERSÃO MELHORADA */
@media (max-width: 768px) {
  /* Garante que o body ocupe toda a altura */
  html, body {
    height: 100% !important;
    min-height: 100vh !important;
    min-height: 100dvh !important;
    overflow-x: hidden !important;
    position: relative !important;
  }

  /* Força centralização e layout responsivo */
  .centered-menu {
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: flex-start !important;
    text-align: center !important;
    padding: 1rem 1rem 4rem 1rem !important;
    width: 100% !important;
    min-height: 100vh !important;
    min-height: 100dvh !important;
    height: auto !important;
    box-sizing: border-box !important;
    overflow-y: auto !important;
    overflow-x: hidden !important;
    -webkit-overflow-scrolling: touch !important;
    position: relative !important;
  }

  /* Layout do jogo principal em mobile */
  main {
    flex-direction: column !important;
    align-items: center !important;
    padding: 0.5rem !important;
    gap: 1rem !important;
    width: 100% !important;
    max-width: 100% !important;
  }

  /* Header responsivo */
  header {
    position: relative !important;
    top: 0 !important;
    width: 100% !important;
    max-width: 100% !important;
    height: auto !important;
    margin-bottom: 1rem !important;
    padding: 1rem !important;
    border-radius: 12px !important;
    box-sizing: border-box !important;
  }

  /* Grid de personagens otimizado para mobile */
  .character-grid {
    grid-template-columns: repeat(3, 1fr) !important;
    gap: 0.8rem !important;
    padding: 0.5rem !important;
    width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
  }

  .character {
    min-height: 120px !important;
    padding: 0.3rem !important;
    border-radius: 8px !important;
    transition: all 0.2s ease !important;
  }

  .character img {
    border-radius: 6px !important;
  }

  /* Força visibilidade dos botões */
  .menu-buttons {
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    gap: 1rem !important;
    width: 100% !important;
    margin-top: 1.5rem !important;
    margin-bottom: 2rem !important;
    padding-bottom: 2rem !important;
  }

  .menu-button {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    width: 100% !important;
    max-width: 300px !important;
    min-height: 52px !important;
    padding: 1rem 1.5rem !important;
    font-size: 1rem !important;
    font-weight: 600 !important;
    border-radius: 12px !important;
    background-color: #2a2a2a !important;
    color: #fff !important;
    border: 2px solid #3a3a3a !important;
    text-decoration: none !important;
    cursor: pointer !important;
    box-sizing: border-box !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: relative !important;
    z-index: 100 !important;
    margin: 0 !important;
    transition: all 0.3s ease !important;
    /* Melhor área de toque */
    touch-action: manipulation !important;
    -webkit-tap-highlight-color: transparent !important;
  }

  .menu-button:active {
    transform: scale(0.98) !important;
  }

  /* Força estilo dos botões primários */
  .menu-button.primary {
    background: linear-gradient(135deg, #4a90e2, #7b68ee) !important;
    border: none !important;
    color: white !important;
    font-weight: 600 !important;
    font-size: 1.1rem !important;
    box-shadow: 0 4px 16px rgba(74, 144, 226, 0.3) !important;
  }

  .menu-button.secondary {
    background: rgba(255, 255, 255, 0.1) !important;
    border: 2px solid rgba(255, 255, 255, 0.2) !important;
    color: white !important;
  }

  /* Força visibilidade do texto */
  .game-logo h1 {
    font-size: 2.2rem !important;
    margin-bottom: 0.5rem !important;
    text-align: center !important;
    color: #fff !important;
    line-height: 1.2 !important;
  }

  .game-subtitle {
    font-size: 0.9rem !important;
    margin-bottom: 1.5rem !important;
    text-align: center !important;
    color: #b0b0b0 !important;
    line-height: 1.4 !important;
  }

  /* Força layout do tutorial */
  .tutorial-section {
    width: 100% !important;
    padding: 1rem !important;
    margin: 1rem 0 !important;
    text-align: center !important;
    border-radius: 12px !important;
  }

  .tutorial-grid {
    display: grid !important;
    grid-template-columns: 1fr !important;
    gap: 1rem !important;
    width: 100% !important;
  }

  .tutorial-item {
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    text-align: center !important;
    padding: 1rem !important;
    background: rgba(255, 255, 255, 0.05) !important;
    border-radius: 12px !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    transition: all 0.3s ease !important;
  }

  .tutorial-item:hover {
    background: rgba(255, 255, 255, 0.08) !important;
    transform: translateY(-1px) !important;
  }

  .tutorial-icon {
    font-size: 2rem !important;
    margin-bottom: 0.5rem !important;
  }

  .tutorial-text {
    margin-left: 0 !important;
    text-align: center !important;
  }

  .tutorial-text strong {
    font-size: 1rem !important;
    color: #fff !important;
    display: block !important;
    margin-bottom: 0.5rem !important;
  }

  .tutorial-text p {
    font-size: 0.85rem !important;
    line-height: 1.4 !important;
    color: #b0b0b0 !important;
    margin: 0 !important;
  }

  /* Melhorias para formulários */
  input[type="text"],
  input[type="email"],
  select {
    font-size: 16px !important; /* Previne zoom em iOS */
    padding: 12px 16px !important;
    min-height: 48px !important;
    border-radius: 8px !important;
    border: 2px solid #3a3a3a !important;
    background: #2a2a2a !important;
    color: #fff !important;
    width: 100% !important;
    box-sizing: border-box !important;
    -webkit-appearance: none !important;
    appearance: none !important;
  }

  /* Melhorias para dropdown */
  .dropdown-content {
    max-height: 250px !important;
    border-radius: 8px !important;
    width: 100% !important;
    left: 0 !important;
    right: 0 !important;
  }

  .dropdown-item {
    padding: 12px 16px !important;
    font-size: 15px !important;
    min-height: 48px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
  }

  /* Contador de pontos */
  #point-counter {
    font-size: 16px !important;
    padding: 0.8rem 1rem !important;
    text-align: center !important;
    background: rgba(255, 255, 255, 0.05) !important;
    border-radius: 8px !important;
    margin: 1rem 0 !important;
  }

  /* Personagem escolhido */
  #chosenDisplay {
    max-width: 200px !important;
    margin: 1rem auto !important;
  }

  #chosenCharacterBox {
    width: 120px !important;
    height: 160px !important;
    margin: 0 auto !important;
    border-radius: 8px !important;
  }
}
